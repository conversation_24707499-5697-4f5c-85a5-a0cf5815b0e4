<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useTaskListRuntimeStore } from '@/views/TaskList/TaskList.ts';
import { TaskView } from '@/components/Tasks';
import type { IMenuItem } from './templates/Menu.ts';
import { TaskTopMenu, TaskGroupMenu, TaskBottomMenu, TaskTagMenu } from './templates';
import TaskFilterMenu from '@/views/TaskList/templates/TaskFilterMenu.vue';

// 当前选中的分类
const categoryMap = ref<Record<string, IMenuItem[]>>({
  top: [],
  group: [],
  taskTag: [],
  bottom: [],
  filter: [],
});
const categoryList = computed<IMenuItem[]>(() => {
  return (
    Object.keys(categoryMap.value)
      .map((key) => categoryMap.value[key])
      .flat() || []
  );
});
const currentCategoryId = ref<string>('toDay');
const currentCategory = computed(() => {
  return categoryList.value.find((item) => item.id === currentCategoryId.value);
});

function setCategoryList(type: string, menuList: IMenuItem[]) {
  console.log('menuList', menuList);
  if (menuList.length === (categoryMap.value[type] || []).length) {
    return;
  }
  categoryMap.value[type] = menuList;
}

const { collapsed } = useTaskListRuntimeStore();
</script>

<template>
  <div class="u-main-content">
    <div class="task-container">
      <!-- 左侧导航栏 -->
      <div class="sidebar" :class="{ collapsed }">
        <a-scrollbar style="max-height: calc(100vh - 55px); overflow-y: auto">
          <div style="padding-bottom: 10px">
            <a-collapse>
              <TaskTopMenu
                v-model:active="currentCategoryId"
                :setCategoryList="(list: IMenuItem[]) => setCategoryList('top', list)"
              />
              <TaskGroupMenu
                v-model:active="currentCategoryId"
                :setCategoryList="(list: IMenuItem[]) => setCategoryList('group', list)"
              />
              <TaskFilterMenu v-model:active="currentCategoryId"
                              :setCategoryList="(list: IMenuItem[]) => setCategoryList('filter', list)" />
              <TaskTagMenu
                v-model:active="currentCategoryId"
                :setCategoryList="(list: IMenuItem[]) => setCategoryList('taskTag', list)"
              />
              <TaskBottomMenu
                v-model:active="currentCategoryId"
                :setCategoryList="(list: IMenuItem[]) => setCategoryList('bottom', list)"
              >
              </TaskBottomMenu>
            </a-collapse>
          </div>
        </a-scrollbar>
      </div>

      <!-- 右侧任务列表 -->
      <div v-if="currentCategory" class="task-list">
        <TaskView
          :title="currentCategory.name"
          :subtitle="currentCategory.desc"
          :id="currentCategory.id"
          :type="currentCategory.menuType"
          :searchParams="currentCategory.searchParams"
          :defaultViewConfig="currentCategory.viewConfig"
          :scene="
            currentCategory.id !== 'finish' && currentCategory.id !== 'delete'
              ? 'default'
              : currentCategory.id
          "
        >
          <template #header-title-prefix>
            <div class="collapsed">
              <div v-if="collapsed" class="u-fx u-fac" @click="collapsed = false">
                <iconpark-icon name="expand-left" />
              </div>
              <div v-else class="u-fx u-fac" @click="collapsed = true">
                <iconpark-icon name="expand-right" />
              </div>
            </div>
            <slot></slot>
          </template>
        </TaskView>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.u-main-content {
  padding: 0;
  width: 100%;
  height: 100%;
}
.task-container {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.task-list {
  flex-grow: 1;
  padding: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  .collapsed {
    iconpark-icon {
      color: var(--color-neutral-5);
      font-size: 20px;
      transition: all ease-in-out 250ms;
      cursor: pointer;
      &:hover {
        color: var(--color-neutral-8);
      }
    }
  }
}
</style>
