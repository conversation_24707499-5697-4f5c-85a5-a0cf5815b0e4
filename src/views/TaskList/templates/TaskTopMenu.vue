<script setup lang="ts">
// 分类数据
import type { IMenuItem } from '@/views/TaskList/templates/Menu.ts';
import dayjs from 'dayjs';
import { onMounted, ref } from 'vue';
import { ExtensionManager } from '@/extension';
import { useEventListener } from '@vueuse/core';
import { umami } from '@/utils/umami';

const props = defineProps<{
  setCategoryList: (menuItems: IMenuItem[]) => void
}>();


const categories: IMenuItem[] = [
  {
    id: 'toDay',
    name: '今天',
    desc: '让每一天都有成就感',
    menuType: 'system',
    searchParams: {
      taskTimeRange: [
        dayjs().startOf('day').valueOf(),
        dayjs().endOf('day').valueOf()
      ],
    },
  },
  {
    id: 'threeDays',
    name: '三日规',
    desc: '三日回望，三日前瞻，进度尽在掌中',
    menuType: 'system',
    searchParams: {
      taskTimeRange: [
        dayjs().subtract(3, 'day').startOf('day').valueOf(),
        dayjs().add(3, 'day').endOf('day').valueOf()
      ]
    },
    icon: 'calendar',
  },
  {
    id: 'collectBox',
    name: '未分组',
    desc: '杂乱？收进来就是整齐的第一步',
    menuType: 'system',
    searchParams: {
      groupId: 'collectBox',
    },
    icon: 'receive',
  },
  // {
  //   id: 'all',
  //   name: '全部',
  //   desc: '杂乱？收进来就是整齐的第一步',
  //   menuType: 'system',
  //   searchParams: {},
  //   icon: 'receive',
  //   // disableCount: true,
  // },
];
onMounted(() => {
  props.setCategoryList(categories);
  handleStatisticsCategoriesCount();
});

const categoryStatistics = ref<{
  [key: string]: number;
}>({});

function handleStatisticsCategoriesCount() {
  categories.map(async (item) => {
    if (item.disableCount) {
      return;
    }
    await ExtensionManager.getTaskInstance().listTask({
      ...item.searchParams,
    }).then(res => {
      categoryStatistics.value[item.id] = res.total;
      // item.data = res.list;
    });
  });
}



const active = defineModel<string>('active');


useEventListener(window, 'taskList::refreshAll', () => {
   handleStatisticsCategoriesCount();
});
function handleMenuClick(category: IMenuItem) {
  active.value = category.id;
  umami().trackEvent(`页面预览:任务/${category.name}`, {
    title: `任务/${category.name}`,
  });
}
</script>

<template>
  <div class="sidebar-section">
    <div
      v-for="(category, index) in categories" :key="index"
      class="sidebar-item"
      :id="category.id"
      :class="{ active: active === category.id }"
      @click="handleMenuClick(category)"
    >
      <svg
        v-if="category.name === '今天'"
        class="icon"
        xmlns="http://www.w3.org/2000/svg"
        width="18"
        height="18"
        viewBox="0 0 24 24"
      >
        <path
          fill="currentColor"
          d="M 19.6 3.313 L 18.515 3.313 L 18.515 1.141 L 16.343 1.141 L 16.343 3.313 L 7.656 3.313 L 7.656 1.141 L 5.485 1.141 L 5.485 3.313 L 4.399 3.313 C 3.199 3.313 2.227 4.285 2.227 5.484 L 2.227 20.686 C 2.227 21.885 3.199 22.858 4.399 22.858 L 19.6 22.858 C 20.806 22.858 21.772 21.891 21.772 20.686 L 21.772 5.484 C 21.772 4.285 20.8 3.313 19.6 3.313 M 19.6 20.686 L 4.399 20.686 L 4.399 9.828 L 19.6 9.828 L 19.6 20.686 Z M 19.6 7.656 L 4.399 7.656 L 4.399 5.484 L 19.6 5.484 L 19.6 7.656 Z"
          style=""
          transform="matrix(1, 0, 0, 1, 1.7763568394002505e-15, 0)"
        />
        <text
          x="12"
          y="18"
          text-anchor="middle"
          font-weight="bold"
          font-size="9"
          fill="currentColor"
        >
          {{ new Date().getDate() }}
        </text>
      </svg>
      <iconpark-icon v-else-if="category.icon"
                     class="icon"
                     :name="category.icon" />
      <div :class="category.icon"></div>
      <div class="name">{{ category.name }}</div>
      <div class="count" v-if="!category.disableCount">
        {{ categoryStatistics[category.id] || 0 }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
