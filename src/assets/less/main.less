@import "@xiaou66/u-web-ui/dist/u-web-ui.css";
@import '../base.css';
@import "./theme/index";

@import "sidebar";
body {
  //background: var(--utools-background);
}
.u-ban {
  user-select: none;
}
.u-main-content {
  padding: 10px;
  height: 100%;
  .u-main-content-inner {
    padding: 10px 20px;
    height: calc(100% - 62px);
    overflow-y: auto;
  }
}

.no-flex .arco-form-item-label-col-flex {
  flex: none !important;
}
.waterfall-list {
  background: transparent !important;
}

.mini-gap-dropdown-select {
  .arco-dropdown {
    padding: 0;
    border: 1px solid var(--color-fill-2);
  }
}
.min-dropdown-select {
  .arco-dropdown {
    border: 1px solid var(--color-fill-2);
  }
  .min-dropdown-select-options, &.min-dropdown-select-options {
    padding: 0 2px;
    >div {
      padding: 4px;
    }
    .arco-dropdown-option {
      padding: 0 8px;
      font-size: 13px;
      line-height: 24px;
      border-radius: 4px;
      .arco-dropdown-option-icon {
        color: var(--text-second-color);
      }
    }
    .arco-dropdown-option-content {
      width: 100%;
    }
    .active {
      color: var(--selected-text-color);
    }
    .arco-dropdown-option:not(.arco-dropdown-option-disabled):hover {
      &.active {
        color: var(--selected-text-color);
      }
    }
  }
}

.arco-select-view-size-mini {
  .arco-select-view-prefix {
    font-size: 12px;
    padding-right: 4px;
  }
}
.u-transparent {
  &.arco-btn-secondary,
  &.arco-btn-secondary[type='button'],
  &.arco-btn-secondary[type='submit'] {
    background-color: transparent;
  }
  &.arco-btn {
    background-color: transparent;
  }
  &.arco-picker {
    background-color: transparent;
  }
  .arco-select {
    background: transparent;
  }
  &.arco-input-wrapper {
    background: transparent !important;
    &:hover {
      border: 1px solid var(--color-neutral-3);
    }
    &:focus-within {
      border: 1px solid rgb(var(--arcoblue-5));
    }
  }
  &.arco-select-view {
    background: transparent;
  }
}
.u-select-options {
  .arco-select-dropdown-list-wrapper {
    padding: 0 4px;
  }
  .arco-select-option:not(.arco-select-dropdown .arco-select-option-disabled):hover, .arco-select-option-selected {
    border-radius: 6px;
  }
  .arco-select-option-active {
    border-radius: 6px;
  }
  .arco-select-dropdown-footer {
    margin-top: 4px;
  }
  &.small {
    font-size: 12px !important;
    .arco-select-option-content {
      font-size: 14px;
    }
    .arco-select-dropdown .arco-select-option {
      line-height: 28px;
    }
  }
  &.min {
    font-size: 10px !important;
    .arco-select-option-content {
      font-size: 12px;
    }
    .arco-select-dropdown .arco-select-option {
      line-height: 24px;
    }
  }
  &.prefix-2 {
    display: flex;
    justify-content: flex-end;
    .arco-select-dropdown {
      min-width: 118px;
    }
  }
}

.u-date-picker {
  width: 246px;
  .arco-picker-cell-today:after {
    display: none;
  }
  .arco-picker-container-shortcuts-placement-left {
    display: grid;
    grid-template-columns: 100px 1fr;
  }
  .arco-picker-header {
    //display: none;
  }

  .arco-picker-container-panel-only {
    border: none;
  }
  .arco-panel-date-inner {
    border: none;
  }
  .arco-picker-header {
    padding: 4px;
    border: none;
  }
  .arco-picker-week-list {
    padding: 4px;
  }
  .arco-picker-body {
    padding: 14px 1px
  }
  .arco-picker-footer-now-wrapper {
    display: none;
  }
  .arco-picker-cell .arco-picker-date {
    padding: 2px 0;
  }
}
.arco-collapse {
  border: none !important;
  line-height: normal !important;
  .arco-collapse-item-header {
    background: transparent !important;
    line-height: 12px;
    font-size: 12px;
  }
  .arco-collapse-item-header-title {
    font-weight: 700 !important;
  }
  .arco-collapse-item-active > .arco-collapse-item-header {
    border-color: transparent;
  }
  .arco-icon-hover.arco-collapse-item-icon-hover:hover::before {
    background: transparent !important;
  }
  .arco-collapse-item {
    border-bottom: none;

  }
  .arco-collapse-item-content {
    background: transparent;
    padding-left: 14px;
  }
  .arco-collapse-item-header-left {
    padding-left: 32px;
    transform: translateX(-7px);
  }
}
//.arco-table-th {
//  background: transparent !important;
//}
//.arco-table-border .arco-table-tr .arco-table-th {
//  border: none !important;
//}
//.arco-table-tr .arco-table-td {
//  background: transparent;
//}

//.arco-table-td {
//  border-bottom: none !important;
//}

// 滚动条
.arco-scrollbar-track-direction-vertical {
  width: 6px !important;
}

.arco-scrollbar-thumb-direction-vertical .arco-scrollbar-thumb-bar {
  width: 4px !important;
}
.arco-scrollbar-track-direction-horizontal {
  height: 6px !important;
}
.arco-scrollbar-thumb-direction-horizontal .arco-scrollbar-thumb-bar {
  height: 4px !important;
}

.arco-drawer .arco-drawer-body {
  padding: 0;
}
