<script setup lang="ts">
const groupType = defineModel<string>('groupType');
const sortType = defineModel<string>('sortType');
const emits = defineEmits<{
  (e: 'change'): void
}>()
</script>

<template>
  <a-dropdown position="tr"
              class="u-transparent min-dropdown-select" >
    <a-button class="sort-group-button" size="small">
      <template #icon>
        <iconpark-icon size="14" name="sort-two"></iconpark-icon>
      </template>
    </a-button>
    <template #content>
      <div class="min-dropdown-select-options sort-group" style="width: 160px;">
        <div>
          <a-select v-model:model-value="groupType"
                    size="mini"
                    style="width: 100%"
                    :trigger-props="{contentClass: 'u-select-options min prefix-2'}"
                    @change="emits('change')">
            <template #prefix>归类</template>
            <a-option value="taskLevel">优先级</a-option>
            <a-option value="taskGroupId">分组</a-option>
            <a-option value="taskStartDate">时间</a-option>
          </a-select>
        </div>
        <div>
          <a-select v-model:model-value="sortType"
                    size="mini"
                    style="width: 100%"
                    :trigger-props="{contentClass: 'u-select-options min prefix-2'}"
                    @change="emits('change')">
            <template #prefix>排序</template>
            <!--                    <a-option value="taskLevel" >优先级</a-option>-->
            <!--                    <a-option value="createAt">创建时间</a-option>-->
            <a-option value="custom">自定义</a-option>
          </a-select>
        </div>
      </div>
    </template>
  </a-dropdown>
</template>

<style scoped lang="less">

</style>
