<script setup lang="ts">
import dayjs from 'dayjs';
import { computed, inject, ref, h, render } from 'vue';
import { TaskDetailTrigger, useTaskDetailTrigger } from '@/components/Tasks/TaskDetailTrigger';
import TaskConstants from "@/constants/tasks/TaskConstants.ts";
import JsUtil from "@/utils/jsUtil.ts";
const props = defineProps<{
  arg: any;
}>();
const emits = defineEmits<{
  triggerClose: [];
}>();
const isAllDay = computed(() => {
  return (start: string, end: string) => {
    const startDate = dayjs(start);
    const endDate = dayjs(end);
    console.log('start', end, start, endDate.diff(startDate, 'minutes'));
    return endDate.diff(startDate, 'minutes') % 1440 === 0;
  };
});
const taskDetailDrawer = useTaskDetailTrigger({
  close: () => {
    console.log('triggerClose');
    emits('triggerClose');
  },
});

const priority = computed<Record<string, any>>(() => {
  return TaskConstants.PRIORITY_SELECT_OPTIONS_TAG.reduce((obj: Record<string, any>, cur: any) => {
    obj[cur.value] = cur;
    return obj;
  }, {});
});

const taskLevel = computed(() => {
  if (!props.arg.event || !props.arg.event.extendedProps
    || !props.arg.event.extendedProps.taskInfo) {
    return '';
  }
  return props.arg.event.extendedProps.taskInfo.taskLevel || '';
});

const taskStartTime = computed(() => {
  const taskInfo =  props.arg.event.extendedProps.taskInfo;
  if (!taskInfo || !taskInfo.taskStartTime) {
    return '';
  }
  return JsUtil.dateToConvertDayjs(taskInfo.taskStartDate, taskInfo.taskStartTime)
    .format('HH:mm')
})
function handleContextmenu() {
  console.log('111111')
}
</script>

<template>
    <div class="event-item"
         @contextmenu="handleContextmenu"
         @click="(e) => taskDetailDrawer.showTrigger(e, arg.event.extendedProps.taskInfo)"
         :style="{backgroundColor: `rgba(${JsUtil.hexToRgb(taskLevel ? priority[taskLevel].color : '#88909c')}, 0.25)`}">
        <div class="title">
            <div class="status" :style="{backgroundColor: taskLevel ? priority[taskLevel].color : '#88909c'}"></div>
            <span>{{ arg.event.title }}</span>
        </div>
        <div class="time">
            <span v-if="isAllDay(arg.event.start, arg.event.end)"> 全天 </span>
            <span v-else>{{ taskStartTime }}</span>
        </div>
    </div>
</template>

<style scoped lang="less">
.event-item {
  position: relative;
  width: 100%;
  display: flex;
  gap: 3px;
  height: 16px;
  border-radius: 4px;
  //background: rgba(245, 34, 45, 0.55);
  color: var(--text-color);
  &:hover {
  }
  .title {
    display: grid;
    grid-template-columns: 9px auto;
    //align-items: center;
    font-size: 10px;
    font-weight: 500;
    >span {
      padding-top: 3px;
      padding-left: 1px;
      padding-right: 2px;
      white-space:nowrap;
      overflow:hidden;
      text-overflow:ellipsis;
    }
    // 状态
    .status {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      position: relative;
      top: 50%;
      left: 2px;
      transform: translateY(-50%);
    }
  }
  .time {
    display: none;
  }
}
/* 弹框内的事件项样式 */
.u-popover-main {
  .event-item {
    background: rgba(245, 34, 45, 0.55);
    padding: 8px 6px;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    .title {
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 12px;
      flex: 1;
      color: var(--text-color);
      .status {
        width: 4px;
        height: 4px;
        margin-top: 4px;
      }
      >span {
        padding: 0 0 0 2px;
      }
    }
    .time {
      color: #747677;
      display: flex;
    }
  }
}
</style>
