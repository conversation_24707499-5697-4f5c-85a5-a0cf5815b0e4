import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { umami } from '@/utils/umami';
import type { MenuRouterItem } from '@xiaou66/u-web-ui';


// 路由列表
export const routes: MenuRouterItem[] = [
  {
    path: '/taskList',
    name: 'taskList',
    component: () => import('@/views/TaskList/TaskList.vue'),
    meta: {
      title: '任务',
      icon: 'todo',
      menu: true,
    }
  },
  // {
  //   path: '/groupBox',
  //   name: 'groupBox',
  //   component: () => import('@/views/GroupBox/GroupBox.vue'),
  //   meta: {
  //     title: '分组',
  //     icon: 'box',
  //   },
  // },
  {
    path: '/groupBox/groupDetail/:groupId',
    name: 'groupDetail',
    props: true,
    component: () => import('@/views/GroupDetail/GroupDetail.vue'),
    meta: {
      title: '',
      icon: '',
      menu: false,
    }
  },
  {
    path: '/calendarView',
    name: 'calendarView',
    component: () => import('@/views/CalendarView/CalendarView.vue'),
    meta: {
      title: '日历',
      icon: 'calendar',
      menu: true,
    }
  },
  {
    path: '/fourQuadrant',
    name: 'fourQuadrant',
    component: () => import('@/views/FourQuadrant/FourQuadrant.vue'),
    meta: {
      title: '四象限',
      icon: 'grid-two',
      menu: true,
    }
  },
  {
    path: '/collectScene',
    name: 'collectScene',
    component: () => import('@/views/CollectScene/CollectScene.vue'),
    meta: {
      title: '场景',
      icon: 'collect',
      menu: true,
    }
  },
  {
    path: '/dataSetting',
    name: 'dataSetting',
    component: () => import('@/views/DataSetting/DataSetting.vue'),
    meta: {
      title: '数据设置',
      icon: 'data',
      menu: true,
    },
  },
  /*{
    path: '/concentration',
    name: 'concentration',
    component: () => import('@/views/ConcentrationTool/ConcentrationTool.vue'),
    meta: {
      title: '专注',
      icon: 'concentration',
    }
  },*/
  {
    path: '/setting',
    name: 'setting',
    component: () => import('@/views/Setting/SettingHome.vue'),
    meta: {
      title: '设置',
      icon: 'config',
      menu: true,
    }
  },
];


const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes,
})

router.beforeEach((to, from, next) => {
  if (to.meta && to.meta.title) {
    if (!from.meta || from.meta.title !== to.meta.title) {
      umami().trackEvent(`页面预览:${to.meta.title}`, {
        title: to.meta.title,
      }, {
        url: to.path
      });
    }
  }
  next();
})

export default router
