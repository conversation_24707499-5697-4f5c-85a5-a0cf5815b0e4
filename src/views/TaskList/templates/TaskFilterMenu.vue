<script setup lang="ts">
import { useCollapse } from '@/hooks/useCollapse.ts';
import { TaskFilterSaveModal } from "./TaskFilterSaveModal";
import type { TaskFilterSaveModalInstance } from "./TaskFilterSaveModal";
import {h, onMounted, ref, useTemplateRef, nextTick} from "vue";
import {ExtensionManager} from "@/extension";
import type { ITaskFilter } from "@xiaou66/todo-plugin";
import {useEventListener} from "@vueuse/core";
import {Modal} from "@arco-design/web-vue";
import type { IMenuItem } from './index.ts';
import { FlowExecutor } from '@/views/CollectScene/flow';


const props = defineProps<{
  setCategoryList: (menuItems: IMenuItem[]) => void
}>();
const [collapsed, handleCollapse, calcCollapseHeight] = useCollapse();


const taskFilterSaveModalRef = useTemplateRef<TaskFilterSaveModalInstance>('taskFilterSaveModalRef');
const data =ref<ITaskFilter[]>()
async function requestQuery() {
  await ExtensionManager.getTaskFilterInstance().getTaskFilterList()
    .then((list) => {
      data.value = list;
      props.setCategoryList([]);
      props.setCategoryList(list.map(item => {
        return {
          id: item.id,
          name: item.filterName,
          desc: '',
          menuType: 'filter',
          searchParams: {
            ...item,
          }
        } as IMenuItem
      }));
    });

  nextTick(() => {
    calcCollapseHeight();
  }).then(() => {});
}
onMounted(() => {
  requestQuery();
})
useEventListener(window, 'taskList::refreshAll', () => {
  if (!collapsed.value) {
    requestQuery();
  }
});
const active = defineModel<string>('active');
 function handleFilterSaveModelClose(id?: string) {
   requestQuery()
     .then(() => {
       console.log('11111', active.value === id);
       if (id && active.value === id) {
         window.dispatchEvent(new CustomEvent('tasklist::taskView'));
       }
     })
}
function handleDeleteTaskFilter(taskFilter: ITaskFilter) {
  Modal.confirm({
    title: '删除筛选器提醒',
    content: () => h('div', {}, [
      h('div', {}, `确定要删除「${taskFilter.filterName}」筛选器吗？`),
      h('div', {  }, [
        h('span', { style: 'color: #f5222d' }, `分组内的任务彻底删除, `),
        h('span', {style: 'font-weight: bold; color: #f5222d'}, '无法还原')
      ])
    ]),
    cancelText: '放弃',
    okText: '确认删除',
    okButtonProps: {
      status: 'danger'
    },
    onOk: async () => {
      await ExtensionManager.getTaskFilterInstance().deleteTaskFilter(taskFilter.id);
      requestQuery().then(() => {});
      if (active.value === taskFilter.id) {
        // 当前是删除的分组
        active.value = 'toDay';
      }
    }
  });
}
</script>

<template>
  <TaskFilterSaveModal ref="taskFilterSaveModalRef"
    @close="handleFilterSaveModelClose" />
  <!--  分组  -->
  <div class="sidebar-group">
    <div class="sidebar-section">
      <div class="section-title"
           @click="handleCollapse">
        <div class="u-fx u-fac u-pos-rel" >
          <iconpark-icon :name="collapsed ? 'right' : 'down'"
                         :size="15" style="position:relative; left: -2px;"></iconpark-icon>
          <div>筛选器</div>
        </div>
        <div class="u-fx u-fac">
          <iconpark-icon name="plus"
                         @click.stop="taskFilterSaveModalRef?.show()" />
        </div>
      </div>
    </div>
    <div class="sidebar-section item"
         :class="{ collapsed }">
      <div  ref="collapseRef">
        <a-dropdown v-for="(taskFilter) in data"
                    :key="taskFilter.id"
                    class="u-transparent min-dropdown-select"
                    trigger="contextMenu"
                    alignPoint>
          <div class="sidebar-item"
               :class="{ active: active === taskFilter.id }"
               @click="active = taskFilter.id">
            <!--              <div class="icon">🐝</div>-->
            <div class="name">{{ taskFilter.filterName }}</div>
<!--            <div class="count">{{ groupStatistics[group.groupId] || 0 }}</div>-->
          </div>
          <template #content>
            <div class="min-dropdown-select-options">
              <a-doption @click="taskFilterSaveModalRef?.show(taskFilter)">
                <template #icon>
                  <iconpark-icon name="write"
                                 style="color: var(--color-neutral-8)" />
                </template>
                编辑
              </a-doption>
              <a-divider style="margin: 4px 0; padding: 0"></a-divider>
              <a-doption @click="handleDeleteTaskFilter(taskFilter)">
                <template #icon>
                  <iconpark-icon name="delete"
                                 style="color: var(--color-neutral-8)" />
                </template>
                删除
              </a-doption>
            </div>
          </template>
        </a-dropdown>
      </div>
    </div>
  </div>

</template>

<style scoped lang="less">

</style>
