<script setup lang="ts">
import type { TaskListItem, TaskSaveParams, ITaskItem, IGroupInfo } from '@xiaou66/todo-plugin'
import { TaskCheck } from '@/components/check/TaskCheck';
import { TaskDateTimeTrigger } from '@/components/Tasks/TaskCreateInput';
import { EditorTiptap, type EditorTiptapInstance } from '@/components/editor';
import { TaskLevelDropdown, useTaskDetailDrawer } from '@/components/Tasks';
import {ref, useTemplateRef, computed, onMounted, nextTick, h} from 'vue';
import { cloneDeep } from "es-toolkit";
import dayjs from 'dayjs';
import jsUtil from '@/utils/jsUtil.ts';
import { type InputInstance, Message } from '@arco-design/web-vue';
import { ExtensionManager } from '@/extension';
import GroupButtonSelect from "@/components/group/groupSelect/GroupButtonSelect.vue";
import { GroupSelectDropdown } from '@/components/group';
const props = defineProps<{
  taskInfo: TaskListItem | TaskSaveParams;
}>();
const taskInfoInner = ref<TaskListItem | TaskSaveParams>(cloneDeep(props.taskInfo));
const taskDetailTriggerRef = useTemplateRef<HTMLDivElement>('taskDetailTriggerRef');


const isInnerSave = ref(!!props.taskInfo.taskId);

const editorTiptapRef = useTemplateRef<EditorTiptapInstance>('editorTiptapRef');
const taskContent = ref('');
// 编辑器内容
onMounted(() => {
  // 设置编辑器编辑
  if (!taskInfoInner.value || !taskInfoInner.value.taskId) {
    return;
  }
  taskContent.value
  const taskInstance = ExtensionManager.getTaskInstance();
  taskInstance.getTaskDesc(taskInfoInner.value.taskId!).then((desc) => {
    taskContent.value = desc
  });
});
// 保存编辑器
function handleSaveEditor(content: string) {
  if (!taskInfoInner.value || !taskInfoInner.value.taskId) {
    return;
  }
  const taskInstance =  ExtensionManager.getTaskInstance();
  taskInstance.saveTaskDesc(taskInfoInner.value.taskId, content)
}
// 任务时间保存
function handleTaskDateChance(value: TaskListItem) {
  taskInfoInner.value = {
    ...taskInfoInner.value,
    ...value,
  }
  if (!isInnerSave.value) {
    // 创建任务不走内部保存
    return;
  }
  const task = taskInfoInner.value;
  const taskInstance = ExtensionManager.getTaskInstance();
  taskInstance.saveTask(task.taskGroupId || '', taskInfoInner.value);
}
const emits = defineEmits<{
  (e: 'closeStatus', value: boolean): void;
  (e: 'close'): void;
}>();

function handleSaveData(taskField: keyof TaskListItem) {
  if (!isInnerSave.value) {
    // 创建任务不走内部保存
    return;
  }
  const task = taskInfoInner.value;
  const taskInstance = ExtensionManager.getTaskInstance();
  // TODO [需要修改] 其他情况下无需传递 docId
  // @ts-ignore
  taskInstance.saveTask(task.taskGroupId || '', {
    taskId: task.taskId,
    docId: task.docId,
    [taskField]: task[taskField],
  });
}


const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
const startDateFormat = computed<string>(() => {
  const taskInfo = taskInfoInner.value;
  if (!taskInfo.taskStartDate) {
    return '';
  }
  const startDay = dayjs(taskInfo.taskStartDate + ' ' + (taskInfo.taskStartTime || '') );
  let str = `${jsUtil.dateFormat(taskInfo.taskStartDate)}(${weeks[startDay.day()]})`;
  if (taskInfo.taskStartTime) {
    str += ' ' + startDay.format('HH:mm');
  }
  return str;
});
const endDateFormat = computed<string>(() => {
  const taskInfo = taskInfoInner.value;
  if (taskInfo.taskDateType === 'date') {
    return '';
  }
  if (!taskInfo.taskEndDate) {
    return '';
  }
  const endDay = dayjs(taskInfo.taskEndDate + ' ' + (taskInfo.taskEndTime || '') );
  let str = `${jsUtil.dateFormat(taskInfo.taskEndDate)}(${weeks[endDay.day()]})`;
  if (taskInfo.taskEndTime) {
    str += ' ' + endDay.format('HH:mm');
  }
  return ' - ' + str;
});


const checkedStatus = ref(taskInfoInner.value && !!taskInfoInner.value.finish);

function finishTaskStatusAction() {
  const taskItem = taskInfoInner.value;
  const oldStatus = taskItem.taskStatus!;
  ExtensionManager.getTaskInstance().saveTask(taskItem.taskGroupId || '', {
    ...taskItem,
    taskStatus: 100
  } as ITaskItem);
  const messageReturn = Message.success({
    position: 'bottom',
    content: () => h('div', {}, [
      h('div', {class: 'u-fx u-gap10'}, [
        h('div', {}, '任务已完成'),
        h('iconpark-icon', {
          name: "return",
          size: 14,
          style: {
            cursor: 'pointer',
          },
          onClick: () => {
            ExtensionManager.getTaskInstance().restartTask({
              ...taskItem,
              docId: 'finish/' + taskItem.docId,
              taskStatus: oldStatus
            } as ITaskItem);
            checkedStatus.value = false;
            messageReturn.close();
            emits('close');
          }
        }),
      ]),
    ]),
    duration: 3000,
  })
}

function handleFinishTaskStatus(val: boolean) {
  checkedStatus.value = true;
  if (val) {
    setTimeout(() => {
      finishTaskStatusAction();
    }, 300);
  } else {
    // 重完结中移出
    ExtensionManager.getTaskInstance().restartTask(taskInfoInner.value as TaskListItem);
  }
}


const titleInputRef = useTemplateRef<InputInstance>('titleInputRef');
onMounted(() => {
  nextTick(() => {
    titleInputRef.value?.focus();
  })
});
defineExpose<{
  getTaskInfo: () => TaskListItem | TaskSaveParams;
}>({
  getTaskInfo: () => taskInfoInner.value,
});

function handleSelectGroup(value: string) {
  console.log('handleSelectGroup', value);
  taskInfoInner.value.taskGroupId = value;
  if (taskInfoInner.value.taskId) {
    const task = taskInfoInner.value;
    const taskInstance = ExtensionManager.getTaskInstance();
    taskInstance.saveTask(task.taskGroupId || '', taskInfoInner.value);
  }
}

const taskDetailDrawerInstance = useTaskDetailDrawer();
</script>

<template>
  <div ref="taskDetailTriggerRef"
       class="event-trigger">
    <div id="taskDetailTriggerData"
         v-if="!taskInfoInner.taskId"
         :data-taskInfo="JSON.stringify(taskInfoInner)" />
    <div class="header">
      <div class="u-fx u-fac u-f-between">
        <div class="u-fx u-fac u-gap10">
          <task-check v-if="isInnerSave"
                      @change="handleFinishTaskStatus"></task-check>
          <a-divider direction="vertical" style="margin: 0 0px"></a-divider>
          <TaskDateTimeTrigger :task-date-info="taskInfoInner"
                               position="bottom"
                               @save="(value: any) => handleTaskDateChance(value as any)"
                               @open="() => emits('closeStatus', false)"
                               @close="() => emits('closeStatus', true)">
            <div>
              <a-button size="mini">{{startDateFormat}}{{endDateFormat}}</a-button>
            </div>
          </TaskDateTimeTrigger>
        </div>
        <div class="action">
          <a-divider direction="vertical" style="margin: 0 0px"></a-divider>
          <a-button
            v-if="isInnerSave"
            size="mini"
            type="text"
            shape="round"
            @click="taskDetailDrawerInstance?.show(taskInfo as ITaskItem)">
            <template #icon>
              <iconpark-icon name="share"></iconpark-icon>
            </template>
          </a-button>
          <!--                        <a-dropdown>
            <a-button size="mini" type="text" shape="round">
              <template #icon>
                <iconpark-icon name="more"></iconpark-icon>
              </template>
            </a-button>
          </a-dropdown>-->
        </div>
      </div>
      <div class="u-fx u-gap10">
        <div class="title">
          <a-input
            ref="titleInputRef"
            v-model:model-value="taskInfoInner.taskTitle"
            class="u-transparent"
            style="width: 100%"
            @change="handleSaveData('taskTitle')"
          >
            <template #prefix>
              <TaskLevelDropdown
                :popupContainer="taskDetailTriggerRef!"
                v-model:modal-value="taskInfoInner.taskLevel"
                @blur="handleSaveData('taskLevel')"
              />
            </template>
          </a-input>
        </div>
      </div>
    </div>
    <div class="body">
      <div class="u-fx  u-gap10 editor">
        <EditorTiptap ref="editorTiptapRef"
                      v-model:modal-value="taskContent"
                      editorContentMaxHeight="200px"
                      hideToolBar
                      @save="handleSaveEditor">
        </EditorTiptap>
      </div>
    </div>
    <div class="u-fx u-f-between footer">
      <div></div>
      <div>
        <GroupButtonSelect v-model:model-value="taskInfoInner.taskGroupId"
                           @change="handleSelectGroup" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.event-trigger {
  width: 340px;
  //height: 400px;
  background: var(--main-background-transparent);
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
  overflow: hidden;
  .header {
    color: rgb(80, 131, 251);
    >div:first-child {
      box-shadow: rgba(33, 35, 38, 0.1) 0px 10px 1px -10px;
      padding: 10px 10px 4px;
    }
    .action {
      display: flex;
      justify-content: flex-end;
      gap: 4px;
      .arco-btn-text[type='button'] {
        color: var(--text-color);
      }

      .arco-btn-text[type='button']:hover {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 6px;
        font-weight: 700;
        color: var(--text-color);
      }
    }
    .date {
      font-size: 12px;
    }
    .title {
      width: 100%;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      padding: 10px 10px 4px;
    }
  }
  .body {
    padding: 0px 12px 8px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    iconpark-icon {
      color: var(--text-color);
    }
    .content {
      font-size: 12px;
    }
    .editor {
      min-height: 200px;
    }
  }
  .footer {
    padding: 6px;
  }
}
</style>
