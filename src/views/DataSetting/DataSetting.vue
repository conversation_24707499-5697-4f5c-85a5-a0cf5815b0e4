<script lang="ts" setup>
import { h, onMounted, ref, toRefs } from 'vue';
// import { useSettingUserStore } from '@/stores/SettingUserStore'
import { Message, Modal, Notification } from '@arco-design/web-vue'
import DataSettingConfig from "@/views/DataSetting/DataSettingConfig.ts";
import FileUtils from "@/utils/FileUtils.ts";
import dayjs from "dayjs";
// import DataUtils from '@/utils/DataUtils'

const { localAppDataPath } = DataSettingConfig;
const model = ref();

function handlerOpenLocalAppDataPath() {
  FileUtils.createDirIfAbsent(localAppDataPath.value);
  utools.shellShowItemInFolder(localAppDataPath.value);
}


// 删除插件所有数据
function handleDeleteAllPluginData() {
  Modal.warning({
    title: '二次确认',
    content: '确认要删除插件的目录和插件内所有的数据?',
    okText: '删除',
    cancelText: '取消',
    hideCancel: false,
    okButtonProps: {
      status: 'danger'
    },
    onOk(e) {
      utools.db.allDocs()
          .map(({_id}) => utools.dbStorage.removeItem(_id))
      window.fs.rmSync(localAppDataPath.value, {
        recursive: true,
        force: true
      });
      utools.getFeatures()
          .map(({ code }) => utools.removeFeature(code));
      Notification.success({
        title: '提示',
        content: '清理成功, 需要完全退出插件后再进入即可'
      });
      // @ts-ignore
      utools.outPlugin(true);
    },
  });
}

// 删除临时数据文件夹
function handleDeleteTempData() {
  Modal.warning({
    title: '二次确认',
    content: '确认要清除插件数据不包括备份?',
    okText: '删除',
    cancelText: '取消',
    hideCancel: false,
    okButtonProps: {
      status: 'danger'
    },
    onOk() {
      utools.db.allDocs()
        .map(item => utools.dbStorage.removeItem(item._id));
    },
  });
}

function backUpData() {
  const dataList = utools.db.allDocs().map(({value, _id}) => ({ _id, value }));
  JSON.stringify(dataList);
  const bakPath = window.path.join(localAppDataPath.value, 'backups');
  FileUtils.createDirIfAbsent(bakPath);
  const {VITE_APP_VERSION, VITE_ENV_NAME} = import.meta.env;
  const fileName = `备份#${VITE_ENV_NAME}#${dayjs().format("YYYY-MM-DD HH_mm_ss")}#manual#${VITE_APP_VERSION}.bak`;
  window.fs.writeFileSync(window.path.join(bakPath, fileName), JSON.stringify(dataList));
  refreshBackupsList();
  Message.success("备份完成");
}
interface IBackupsItem {
  pluginVersion: string,
  pluginType: string,
  backupsType: 'manual' | 'auto',
  time: string,
  backupsFileName: string
}
const backupsList = ref<IBackupsItem[]>([])

function refreshBackupsList() {
  const bakPath = window.path.join(localAppDataPath.value, 'backups')
  FileUtils.createDirIfAbsent(bakPath);
  const backupsFileNameList = window.fs.readdirSync(window.path.join(bakPath))
    .filter(item => item.startsWith("备份") && item.endsWith('.bak'));
  backupsList.value = backupsFileNameList.map(backupsFileName => {
    const [name, pluginType, time, backupsType, pluginVersion] = backupsFileName.replace(window.path.extname(backupsFileName), '')
      .split("#");
    return {
      pluginType,
      pluginVersion: 'v' + pluginVersion.toString().replace(/_/g, '.'),
      backupsType: backupsType as 'manual' | 'auto',
      time: time.toString().replace(/_/g, ':'),
      backupsFileName
    }
  }).sort((a, b) => dayjs(a.time).isAfter(dayjs(b.time)) ? -1 : 1)
}
onMounted(() => {
  refreshBackupsList();
});

function restoreBackup(backups: IBackupsItem) {
  const bakPath = window.path.join(localAppDataPath.value, 'backups')
  const jsonStr = window.fs.readFileSync(window.path.join(bakPath, backups.backupsFileName), 'utf-8');
  utools.db.allDocs()
    .map(item => utools.dbStorage.removeItem(item._id));
  utools.db.bulkDocs(JSON.parse(jsonStr));
  Message.success("还原备份成功");
}

function deleteBackUp(backups: IBackupsItem) {
  const bakPath = window.path.join(localAppDataPath.value, 'backups')
  window.fs.rmSync(window.path.join(bakPath, backups.backupsFileName));
  Message.success("删除备份成功");
  refreshBackupsList();
}
</script>
<template>
  <div class="u-main-content">
    <a-page-header :show-back="false">
      <template #title>数据管理</template>
      <template #subtitle>
        这里是可以管理插件数据, 如果需要管理插件数据均在这里配置
      </template>
    </a-page-header>
    <div class="u-gap10 u-radius10" style="padding: 14px; flex-direction: column; background: var(--color-bg-3);">
      <div class="u-fac u-f-between u-gap10"  style="display: grid; grid-template-columns: 1fr 60px">
        <div class="u-fx u-fac u-gap5" style="width: 100%">
          <div style="width: 110px;">插件数据目录: </div>
          <div style="width: 100%">
            <a-input class="u-transparent"
                        :model-value="localAppDataPath"
                        style="width: 100%"
                        size="small"
                        readonly />
          </div>
        </div>
        <div>
          <a-link @click="handlerOpenLocalAppDataPath">
            <iconpark-icon name="folder-open" style="padding-right: 4px;"></iconpark-icon>
            打开
          </a-link>
        </div>
      </div>
      <div class="u-tips u-mt10">存储插件额外需要支持库及运行时部分缓存, 无需清理插件会定期清理</div>
    </div>
    <div class="u-radius10" style="background: var(--color-bg-3); margin-top: 20px; padding: 10px">
      <div class="u-fx" style="justify-content: flex-end; padding: 0px 0px 10px">
        <a-button-group shape="round">
          <a-button @click="backUpData">
            <template #icon>
              <div class="u-fx u-fac">
                <iconpark-icon name="history"></iconpark-icon>
              </div>
            </template>
            备份
          </a-button>
          <a-button  @click="handleDeleteTempData">
            <template #icon>
              <div class="u-fx u-fac">
                <iconpark-icon name="delete"></iconpark-icon>
              </div>
            </template>
            清理插件数据
          </a-button>
          <a-button status="danger"  @click="handleDeleteAllPluginData" >
            <template #icon>
              <div class="u-fx u-fac">
                <iconpark-icon name="delete"></iconpark-icon>
              </div>
            </template>
            清理全部数据
          </a-button>
        </a-button-group>
      </div>
      <a-table :data="backupsList"
               :pagination="false"
               size="small"
               :bordered="false"
               :scroll="{ y: 250 }">
        <template #columns>
          <a-table-column data-index="time" title="备份时间" />
          <a-table-column data-index="pluginType" title="插件类型" :width="150" />
          <a-table-column data-index="pluginVersion" title="插件版本" :width="100" />
          <a-table-column data-index="backupsType" title="备份类型" :width="88">
            <template #cell="{record}">
              <a-tag v-if="record.backupsType==='manual'" color="blue" size="small">
                手动
              </a-tag>
              <a-tag v-else  color="green" size="small">
                自动
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column data-index="backupsType" title="操作" :width="100">
            <template #cell="{record}">
              <a-popconfirm content="确认要删除这个备份"
                            :ok-button-props="{status: 'danger'}"
                            @ok="deleteBackUp(record)">
                <a-link style="font-size: 13px" status="danger">删除</a-link>
              </a-popconfirm>

              <a-popconfirm content="确认要数据还原到这个版本"
                            @ok="restoreBackup(record)">
                <a-link style="font-size: 13px">还原</a-link>
              </a-popconfirm>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
  </div>
</template>
<style lang="less" scoped>
.tips {
  margin-bottom: 16px;
}
:deep(.arco-table-tr:last-child .arco-table-td) {
  border-bottom: none;
}
:deep(.arco-table-body) {
  min-height: 32px;
}
</style>
