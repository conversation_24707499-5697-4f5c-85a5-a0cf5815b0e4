<script setup lang="ts">
import FullCalendar from '@fullcalendar/vue3';
import type { CalendarOptions, Calendar } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
// @ts-ignore
import LunarCalendar from 'lunar-calendar';
import {
  reactive,
  ref,
  onMounted,
  h,
  render,
  useTemplateRef,
  onBeforeUnmount,
  computed
} from 'vue';
import dayjs from 'dayjs';
import { EventImpl } from '@fullcalendar/core/internal';
import { EventItem } from '@/components/calendar';
import { ExtensionManager } from '@/extension';
import type {  CalendarDataItem } from './CalendarView.ts';
import { useTaskDetailTrigger } from '@/components/Tasks/TaskDetailTrigger';
import jsUtil from '@/utils/jsUtil.ts';
import { useEventListener } from '@vueuse/core';
import { useTaskContextMenu } from '@/components/Tasks';
import type { IGroupInfo, TaskListParams } from '@xiaou66/todo-plugin';

const nowMonth = ref(dayjs());
const longPressTimer = ref<number | null>(null);
const longPressDuration = 300; // 长按时间阈值，单位毫秒
const isLongPress = ref(false);

const calendarRef = useTemplateRef<{
  getApi(): Calendar;
  buildOptions(suppliedOptions: CalendarOptions | undefined): CalendarOptions;
}>('calendarRef');

const calendarData = ref<CalendarDataItem[]>([]);
const taskListSearchParams = ref<TaskListParams>({ taskGroupIdList: [] });
async function requestQuery() {
  console.log('CalendarView--requestQuery')
  const taskList = await ExtensionManager.getTaskInstance().listTask({
    taskTimeRange: [
      nowMonth.value.startOf('month').valueOf(),
      nowMonth.value.endOf('month').valueOf(),
    ],
    ...taskListSearchParams.value,
  });
  calendarData.value = taskList.list.map((item) => {
    let end = undefined;
    if (item.taskDateType !== 'date') {
      end = jsUtil.dateToConvertStr(item.taskEndDate, item.taskEndTime);
      if (!item.taskEndTime) {
        end += ' 23:59:59'
      }

    }
    return {
      id: item.taskId,
      title: item.taskTitle,
      start: jsUtil.dateToConvertStr(item.taskStartDate, item.taskStartTime),
      end,
      extendedProps: {
        taskInfo: item,
      },
    };
  });
  console.log('calendarData.value', calendarData.value);
  // 更新日历事件
  if (calendarRef.value) {
    const calendarApi = calendarRef.value.getApi();
    calendarApi.removeAllEvents();
    console.log('calendarRef.value', calendarRef.value);
    calendarRef.value.getApi().addEventSource(calendarData.value)
    // for (const eventSource of calendarData.value) {
    //   console.log('eventSource', eventSource);
    //   calendarApi.addEvent(eventSource);
    // }
  }
  console.log('requestQuery', taskList.list || []);
}
useEventListener(window, 'taskList::refreshAll', () => {
  requestQuery();
});
onMounted(() => {
  requestQuery();
});
const prepareCreateEvent = ref<EventImpl>();
const taskDetailTrigger = useTaskDetailTrigger({
  close() {
    requestQuery();
  },
});

const currentCreateGroupId = computed(() => {
  return taskListSearchParams.value.taskGroupIdList && taskListSearchParams.value.taskGroupIdList.length > 0
    ?  taskListSearchParams.value.taskGroupIdList[0] : 'collectBox'
});
const calendarOptions = reactive<CalendarOptions>({
  height: '100%',
  plugins: [dayGridPlugin, interactionPlugin],
  initialView: 'dayGridMonth', // 默认为那个视图（月：dayGridMonth，周：timeGridWeek，日：timeGridDay）
  timeZone: 'Asia/Shangai',
  weekends: true, // 显示周末
  navLinks: false, //日期是否可以被点击
  headerToolbar: false, // 隐藏工具
  dayMaxEvents: getMaxEventsForScreenSize(), // 根据屏幕大小设置最大事件数
  firstDay: 0, // 设置一周中显示的第一天是哪天，周日是0，周一是1，类推  new Date().getDay()当前天
  locale: 'zh-cn', // 切换语言，当前为中文
  unselectAuto: false, //当点击页⾯⽇历以外的位置时，是否⾃动取消当前的选中状态。false是不取消
  selectable: true, //是否可以选中日历格
  dayCellClassNames: 'month-day-cell', //单元格类名
  nowIndicator: true, //是否显示时间线
  slotLabelFormat: {
    //格式化小时的 -view 时间格式
    hour: '2-digit',
    minute: '2-digit',
    omitZeroMinute: false,
    hour12: false, // 设置时间为24小时
  },
  selectAllow: (row) => {
    // 记录选择开始时间，启动长按计时器
    if (isLongPress.value) {
      // 长按时创建事件
      if (!prepareCreateEvent.value) {
        // prepareCreateEvent.value = calendarRef.value?.getApi().addEvent({
        //   id: Date.now().toString(),
        //   title: '新建日程11',
        //   start: row.start,
        //   end: row.end,
        // })!;
      } else {
        prepareCreateEvent.value?.setStart(row.start);
        prepareCreateEvent.value?.setEnd(row.end);
      }
    } else {
      longPressTimer.value = window.setTimeout(() => {
        isLongPress.value = true;
        // 长按时创建事件
        if (!prepareCreateEvent.value) {
          prepareCreateEvent.value = calendarRef.value?.getApi().addEvent({
            id: '',
            title: '新建日程',
            start: row.start,
            end: row.end,
          })!;
        } else {
          prepareCreateEvent.value?.setStart(row.start);
          prepareCreateEvent.value?.setEnd(row.end);
        }
      }, longPressDuration);
    }

    return true;
  },
  select: (row) => {
    // 清除长按计时器
    console.log('select-row', row.jsEvent);
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value);
      longPressTimer.value = null;
    }
    if (isLongPress.value) {
      taskDetailTrigger.showTrigger(row.jsEvent!, {
        children: [],
        taskStatus: 0,
        taskTitle: '',
        taskGroupId: currentCreateGroupId.value,
        taskDateType:
          dayjs(row.endStr).subtract(1, 'day').format('YYYY-MM-DD') === row.startStr
            ? 'date'
            : 'dateSegment',
        taskStartDate: row.startStr,
        taskEndDate: dayjs(row.endStr).format('YYYY-MM-DD'),
        taskStartTime: '',
        taskEndTime: '',
        taskLevel: '2',
      });
      // 重置长按状态
      isLongPress.value = false;
      prepareCreateEvent.value = undefined;
    }
  },
  // 自定义更多事件弹出框
  moreLinkClick: function (info) {
    // 直接返回字符串 'popover' 使用默认弹出框
    return 'popover';
  },
  dayCellDidMount: function (arg) {
    let down = 0;
    arg.el.addEventListener('mousedown', (e: MouseEvent) => {
      if (e.button !== 0) {
        return
      }
      if (Date.now() - down < 300) {
        calendarRef.value?.getApi().addEvent({
          id: '',
          title: '新建日程',
          start: arg.date,
          end: arg.date,
        });
        console.log('taskGroupIdList', taskListSearchParams.value);
        taskDetailTrigger.showTrigger(e, {
          children: [],
          taskStatus: 0,
          taskTitle: '',
          taskGroupId: currentCreateGroupId.value,
          taskDateType: 'date',
          taskStartDate: dayjs(arg.date).format('YYYY-MM-DD'),
          taskEndDate: dayjs(arg.date).format('YYYY-MM-DD'),
          taskStartTime: '',
          taskEndTime: '',
          taskLevel: '2',
        });
        console.log('dayCellDidMount', arg);
      }
      down = Date.now();
    })
    // console.log('dayCellDidMount', arg)
  },
  // 添加事件渲染后的回调，用于自定义弹出框
  eventDidMount: function (arg) {
    // 这里可以添加事件监听或修改DOM
    //添加右键菜单
    arg.el.addEventListener('contextmenu', (e) => {
      //阻止浏览器的默认右键菜单
      console.log('右键----');
      useTaskContextMenu()
        .show(arg.event.extendedProps.taskInfo as any, e);
      // contextRef.value?.show();
    })
  },
  // 处理事件点击
  eventClick: function (info) {
    // 阻止事件冒泡，防止弹窗关闭
    info.jsEvent.stopPropagation();
  },
});

/**
 * 根据日期格子高度计算适当的最大事件数
 */
function getMaxEventsForScreenSize(): number {
  // 获取日期格子的高度
  const dayCells = document.querySelectorAll('.fc-daygrid-day');
  Array.from(dayCells).map((dayCell) => {
    ((dayCell as HTMLElement).querySelector('.fc-daygrid-day-frame') as HTMLElement).style.height =
      '50px';
  });
  if (dayCells.length > 0) {
    const cell = dayCells[0] as HTMLElement;
    const cellHeight = cell.offsetHeight;
    console.log('cellHeight', cellHeight);

    // 每个事件项大约需要22px高度（包括间距）
    // 减去日期数字所占空间（约28px）和底部空间（约10px）
    const availableHeight = cellHeight - 26;
    // 计算可以容纳的事件数量
    const maxEvents = Math.max(1, Math.floor(availableHeight / 18)) - 1;
    return maxEvents;
  }

  // 如果还没有渲染日期格子，则使用基于窗口大小的默认值
  const height = window.innerHeight;
  if (height < 768) {
    return 1; // 移动设备
  } else if (height < 1024) {
    return 2; // 平板设备
  } else if (height < 1440) {
    return 3; // 小型桌面
  } else {
    return 4; // 大型桌面
  }
}

/**
 * 更新日历的最大事件数
 */
function updateMaxEvents() {
  const api = calendarRef.value?.getApi();
  if (api) {
    // 短暂延迟确保DOM已更新
    setTimeout(() => {
      api.setOption('dayMaxEvents', getMaxEventsForScreenSize());
    }, 100);
  }
}

/**
 * 跳转至指定日期
 * @param dateStr 确认字符串
 * @param selected 是否选择
 */
function toDataDay(dateStr: string, selected = true) {
  if (!calendarRef.value) {
    return;
  }
  calendarRef.value.getApi().gotoDate(dateStr);
  selected && calendarRef.value.getApi().select({ start: dateStr });
  nowMonth.value = dayjs(calendarRef.value.getApi().getDate());
  // requestQuery();
}

// 切换到上一个月
function toPre() {
  if (!calendarRef.value) {
    return;
  }
  const calendarApi = calendarRef.value.getApi();
  const currentDate = dayjs(calendarApi.getDate()).subtract(1, 'month').format('YYYYMMDD');
  toDataDay(currentDate, false);
  requestQuery().then(() => {})
}

// 切换到下一个月
function toNext() {
  // const calendarApi = minCalendarRef.value.getApi()
  if (!calendarRef.value) {
    return;
  }
  const calendarApi = calendarRef.value.getApi();
  const currentDate = dayjs(calendarApi.getDate()).add(1, 'month').format('YYYYMMDD'); // 获取当前显示的日期
  toDataDay(currentDate, false);
  requestQuery().then(() => {})
}

// 跳转到今天日期
function toNow() {
  toDataDay(dayjs().format('YYYYMMDD'), true);
  requestQuery().then(() => {})
}

// 在日历加载后自定义弹出框
onMounted(() => {
  // 监听DOM变化，当弹出框出现时进行自定义
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes.length) {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1 && (node as Element).classList.contains('fc-popover')) {
            console.log('弹出框已添加到DOM', node);
            customizePopover(node as HTMLElement);
          }
          if (node.nodeType === 1 && (node as Element).classList.contains('event-trigger')) {
            console.log('event-trigger 已添加到DOM', node);
          }
        });
      }
    });
  });

  // 开始观察文档变化
  // @ts-ignore
  observer.observe(document.body, { childList: true, subtree: true });
  console.log('开始观察DOM变化');

  // 添加窗口大小变化监听
  window.addEventListener('resize', updateMaxEvents);

  // 初始渲染后计算一次
  setTimeout(() => {
    updateMaxEvents();
  }, 300);
});

// 在组件卸载时移除事件监听器
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateMaxEvents);
  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value);
    longPressTimer.value = null;
  }
});

const popoverValue = ref<HTMLElement>();
const popupVisible = ref(false);

function stopPopover(e: MouseEvent) {
  if (!popupVisible.value) {
    return;
  }
  console.log(document.querySelector('.event-trigger'));
  if (
    !popoverValue.value!.contains(e.target as Node) &&
    document.body.contains(popoverValue.value!)
  ) {
    e.stopPropagation();
    e.preventDefault();
    return false;
  }
}
// 自定义弹出框函数
function customizePopover(popover: HTMLElement) {
  popover.style.opacity = '0';
  popover.classList.add('u-popover-main');
  console.log('正在自定义弹出框', popover);
  // 获取弹出框标题和内容区域
  const header = popover.querySelector('.fc-popover-header');
  const body = popover.querySelector('.fc-popover-body');

  if (header && body) {
    // 清除原有标题内容并添加自定义标题
    const title = header.querySelector('.fc-popover-title')!;
    const dataDate = popover.getAttribute('data-date');
    if (title) {
      const day = dayjs(dataDate);
      const lunarDate = LunarCalendar.solarToLunar(day.year(), day.month() + 1, day.date());
      const vNode = h('div', { class: 'u-popover-title u-fx u-fac u-gap5' }, [
        h('div', { class: 'day' }, day.format('DD')),
        h('div', { class: 'u-tips' }, lunarDate.lunarDayName),
      ]);
      const htmlDivElement = document.createElement('div');
      render(vNode, htmlDivElement);
      header?.replaceChildren(htmlDivElement);
      console.log('innerHTML', title.innerHTML);
    }
    popoverValue.value = popover;

    // 美化事件列表
    const events = body.querySelectorAll('.fc-event');
    events.forEach((event) => {
      // 可以在这里添加自定义样式或结构
      event.classList.add('u-popover-event');
    });

    // 确保弹出框在视图内
    ensurePopoverInView(popover);
  }
}

// 添加新函数确保弹出框在视图内
function ensurePopoverInView(popover: HTMLElement) {
  setTimeout(() => {
    popover = document.getElementById(popover.id)!;
    // 获取弹出框和日历容器的位置信息
    const popoverRect = popover.getBoundingClientRect();
    // @ts-ignore
    const calendarEl = calendarRef.value?.$el;

    if (!calendarEl) return;

    const calendarRect = calendarEl.getBoundingClientRect();

    // 检查弹出框是否超出日历容器顶部
    if (popoverRect.top < calendarRect.top) {
      const overflowY = calendarRect.top - popoverRect.top;
      popover.style.top = `${parseInt(popover.style.top) + overflowY}px`;
    }

    // 检查弹出框是否超出日历容器左侧
    if (popoverRect.left < calendarRect.left) {
      const overflowX = calendarRect.left - popoverRect.left;
      popover.style.left = `${parseInt(popover.style.left) + overflowX}px`;
    }

    // 检查弹出框是否超出日历容器右侧
    if (popoverRect.right > calendarRect.right) {
      const overflowX = popoverRect.right - calendarRect.right;
      popover.style.left = `${parseInt(popover.style.left) - overflowX}px`;
    }

    console.log(calendarRect);
    // 检查弹出框是否超出日历容器底部
    if (popoverRect.bottom > calendarRect.bottom) {
      const overflowY = popoverRect.bottom - calendarRect.bottom + 20; // 添加10px的边距
      popover.style.top = `${parseInt(popover.style.top) - overflowY}px`;
    }
    popover.style.opacity = '1';
  });
}

const festivals = ['国庆节', '劳动节', '元旦节'];

// 计算日历单元格内容函数
function calcDay(arg: any) {
  const day = dayjs(arg.date);
  const lunarDate = LunarCalendar.solarToLunar(day.year(), day.month() + 1, day.date());
  if (day.format('YYYYMMDD') === dayjs().format('YYYYMMDD')) {
    return `<div class="now-day">今</div><span style="color: #c3c3c3">${lunarDate.lunarDayName}</span>`;
  }
  let show = lunarDate.lunarDayName;
  if (lunarDate.lunarFestival) {
    show = lunarDate.lunarFestival;
  } else if (lunarDate.solarFestival) {
    const festival = festivals.find((item) => lunarDate.solarFestival.includes(item));
    if (festival) {
      show = festival;
    }
  } else if (lunarDate.term) {
    show = lunarDate.term;
  }
  return `${day.date()} <span style="color: #c3c3c3">${show}</span>`;
}

const groupList = ref<IGroupInfo[]>([])
async function groupListRequest() {
  const taskInstance = ExtensionManager.getGroupInstance();
  const group = await taskInstance.listGroup({} as any);
  groupList.value = group.list;
}
onMounted(() => {
  groupListRequest();
})
</script>

<template>
  <div class="u-main-content" style="height: 100%; padding: 0 0 10px">
    <div style="height: 100%">
      <div class="calendar-tool">
        <div class="u-fx u-fac u-gap10">
          <t-input-group shape="round" size="small">
            <t-button style="width: 32px"
                      round
                      theme="default"
                      @click="toPre">
              <template #icon>
                <t-icon class="i-u-left" />
              </template>
            </t-button>
            <t-button round @click="toNow()"
                      theme="default">今天</t-button>
            <t-button style="width: 32px"
                      round
                      @click="toNext"
                      theme="default">
              <template #icon>
                <t-icon class="i-u-right" />
              </template>
            </t-button>
          </t-input-group>
          <div style="font-weight: 700">{{ nowMonth.format('YYYY 年 MM 月') }}</div>
        </div>
        <div>
          <a-select v-model:model-value="taskListSearchParams.taskGroupIdList"
                    size="small"
                    style="width: 220px;"
                    allow-clear
                    @change="requestQuery"
                    max-tag-count="1"
                    multiple>
            <template #prefix>分组</template>
            <a-option v-for="item in groupList"
                      :key="item.groupId"
                      :value="item.groupId">
              {{ item.groupName }}
            </a-option>
          </a-select>
        </div>
      </div>
      <div style="height: calc(100% - 42px)">
        <FullCalendar
          class="calendar"
          ref="calendarRef"
          :options="calendarOptions"
          style="height: 100%"
        >
          <template v-slot:dayCellContent="arg">
            <div v-html="calcDay(arg)"></div>
          </template>
          <template v-slot:eventContent="arg">
            <EventItem :arg="arg"
                       @triggerClose="requestQuery" />
          </template>
          <template v-slot:moreLinkContent="arg">
            <span class="more-link">还有 {{ arg.num }} 项</span>
          </template>
        </FullCalendar>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.u-main-content {
  overflow: hidden;
}
// 日历布局
.layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  padding: 16px;
  gap: 16px;
}
// 日历工具
.calendar-tool {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 10px;
  padding: 10px;
  > div {
    display: flex;
  }
  &.min {
    justify-content: end;
    margin-top: 10px;
    font-size: 14px;
  }
}

// 标题
:deep(.fc-col-header-cell-cushion) {
  color: var(--color-neutral-6) !important;
  font-size: 0.9rem !important;
}

// 时间样式
:deep(.fc-daygrid-day-number) {
  display: flex;
  justify-content: center;
  font-size: 0.8rem;
  width: 100%;
  > div {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
  }
}

//region 今天样式
:deep(.now-day) {
  background: #0089ff;
  color: #ffffff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
:deep(.fc-day-today) {
  background-color: transparent !important;
}
// 去掉跨日期样式
:deep(.fc-h-event) {
  background: transparent;
  transition: opacity 220ms linear;
  border: none;
  cursor: pointer;
  padding: 1px 0;
}
//endregion

//region 边框
:deep(.fc-theme-standard td),
:deep(.fc-theme-standard th) {
  border: none;
}

// 分割线
:deep(.fc-theme-standard tr) {
  border: solid 1px var(--color-neutral-3);
}
:deep(.fc-theme-standard td) {
  //border: solid 1px @border-color;
  border-top: none;
}
.calendar {
  :deep(.fc-daygrid-day-number) {
    height: 28px;
  }
  :deep(.fc-daygrid-more-link) {
    display: block;
    width: 90%;
  }
  :deep(.fc-popover) {
    position: absolute;
  }
  /*  :deep(.fc-popover-header) {
    //background: #ffffff;
    //display: none;
  }*/
  :deep(.fc-popover-body) {
    border: none;
    max-height: 200px;
    overflow-y: auto;
  }
  :deep(.fc-more-popover-misc) {
    font-size: 20px;
    display: flex;
    margin-bottom: 6px;
    span {
      font-size: 16px;
    }
  }

  :deep(.fc-daygrid-day-bottom) {
    width: 100%;
    display: flex;
    justify-content: center;
    //background: #c0c1c2;
    border-radius: 10px;
  }
}

:deep(.fc-theme-standard .fc-scrollgrid) {
  border: none;
}
:deep(.fc .fc-daygrid-day-bg .fc-highlight) {
  z-index: -3 !important;
}
//endregion

// 执行
/*
:deep(.fc-highlight) {
  //background: #e0f1ff;
}
*/
:deep(.u-popover-title .u-tips) {
  font-size: 14px;
}

.expand-action {
  margin-top: 10px;
}
.schedule-details {
  font-size: 12px;
}
.more-link {
  font-size: 10px;
  display: block;
  text-align: center;
  width: 100%;
  padding: 1px;
  color: var(--text-color);
}

// 自定义弹出框样式
:deep(.fc-popover) {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #ebeced;
  overflow: hidden;
  max-width: 300px;
}
</style>

<style lang="less">
/*.event-item-wrapper .detail {
  width: 100px;
  height: 100px;
  background: green;
}*/
// 更多弹出框样式
.u-popover-main {
  z-index: 80 !important;
  .arco-trigger-popup {
    transform: translateX(-50px);
  }
  .u-popover-title .day {
    font-size: 32px;
    font-weight: 500;
  }
  .fc-popover-header {
    background-color: var(--color-neutral-1);
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .fc-popover-title {
      display: none;
    }

    .fc-popover-close {
      opacity: 0.7;

      &:hover {
        opacity: 1;
      }
    }
  }
  .fc-popover-body {
    padding: 0 4px 8px !important;
    // 更多弹出框样式第二层样式
    background: var(--color-bg-1);
    .fc-more-popover-misc {
      display: none;
    }
    .u-popover-event {
      padding: 4px;
      border-radius: 6px;
      cursor: pointer;
      // 弹出框样式-任务样式
      &:hover {
        background: var(--color-neutral-2);
      }
    }
    .fc-h-event {
      background: transparent;
    }
  }
}
</style>
