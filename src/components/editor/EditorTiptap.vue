<script setup lang="ts">
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import { DragHandle } from '@tiptap/extension-drag-handle-vue-3'
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight'
import TaskItem from '@tiptap/extension-task-item'
import TaskList from '@tiptap/extension-task-list'
import Underline from '@tiptap/extension-underline'
import Highlight from '@tiptap/extension-highlight'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import TableRow from '@tiptap/extension-table-row'
import Placeholder from '@tiptap/extension-placeholder'
import Gapcursor from '@tiptap/extension-gapcursor'
// import { SlashCommands } from './extensions/SlashCommands'
// import SlashCommandsList from './extensions/SlashCommandsList.vue'
import { onBeforeUnmount, ref, watch } from 'vue';
import type { EditorTiptapInstance, EditorTiptapProps } from '@/components/editor/EditorTiptap.ts'
import HeadlineSelect from '@/components/editor/extensions/HeadlineSelect.vue'
import LinkTool from '@/components/editor/extensions/LinkTool/LinkTool.vue'
import { CustomTable } from '@/components/editor/extensions/CustomTable.ts'
import { CustomLink } from '@/components/editor/extensions/LinkTool/CustomLink.ts'
import TextBubbleMenu from "@/components/editor/extensions/TextBubbleMenu.vue";
import {  createLowlight } from 'lowlight'

// create a lowlight instance
const lowlight = createLowlight()
const props = withDefaults(defineProps<EditorTiptapProps>(), {
  editorOptions: () => ({}),
  hideToolBar: false,
  editorContentMaxHeight: '100%',
});

const modalValue = defineModel<string>('modalValue');
const editor = useEditor({
  content: "",
  extensions: [
    StarterKit,
    TaskList,
    CustomTable.configure({
      resizable: true,
    }),
    TableCell,
    TableRow,
    TableHeader,
    CustomLink.configure({
      openOnClick: false,
      defaultProtocol: 'https',
    }),
    TaskItem.configure({
      nested: true,
    }),
    Highlight.configure({
      multicolor: true,
    }),
    Placeholder.configure({
      showOnlyWhenEditable: false,
      placeholder: ({ editor: Editor}) => {
        if (props.placeholder) {
          return props.placeholder;
        }
        console.log(editor.value?.isEditable)
        if (editor.value?.isEditable) {
          return "输入任务描述..."
        } else {
          return "双击编辑, 请输入任务描述..."
        }
      },
    }),
    CodeBlockLowlight.configure({
      lowlight,
    }),
    Underline,
    Placeholder,
    Gapcursor,
    /*SlashCommands.configure({
      suggestion: {
        items: () => [],
        render: () => {
          let component: VNode | null = null
          let popup: Instance | null = null
          let containerDiv: HTMLElement | null = null
          return {
            onStart: (props: any) => {
              component = h(SlashCommandsList, props)
              containerDiv = document.createElement('div')
              render(component, containerDiv)
              document.body.appendChild(containerDiv)
              popup = tippy('body', {
                getReferenceClientRect: props.clientRect,
                appendTo: () => document.body,
                content: containerDiv,
                showOnCreate: true,
                interactive: true,
                trigger: 'manual',
                placement: 'bottom-start',
                animation: 'scale',
              })[0]
              console.log('component', component)
            },
            onUpdate: (props: any) => {
              if (component) {
                component.props = props
              }
              popup?.setProps({
                getReferenceClientRect: props.clientRect,
              })
            },
            onKeyDown: (props: any) => {
              if (props.event.key === 'Escape') {
                popup?.hide()
                return true
              }
              // @ts-ignore
              return component.ref?.onKeyDown(props)
            },
            onExit: () => {
              popup?.destroy()
              // 卸载组件并移除容器
              if (containerDiv) {
                render(null, containerDiv)
                if (containerDiv.parentNode) {
                  containerDiv.parentNode.removeChild(containerDiv)
                }
                component = null
                popup = null
                containerDiv = null
              }
            },
          }
        },
      },
    }),*/
  ],
  onFocus(props) {
    if (!editor.value) {
      return;
    }
    emits('focus');
  },
  onBlur(props) {
    if (!editor.value) {
      return;
    }
    emits('save', editor.value.getHTML())
  },
  onUpdate: (props) => {
    modalValue.value = editor.value?.getHTML();
  },
  ...props.editorOptions,
});

watch(modalValue, (newValue: any) => {
  if (!editor.value) {
    return;
  }
  if (editor.value.getHTML() !== modalValue.value) {
    editor.value.commands.setContent(newValue, false);
  }
});

onBeforeUnmount(() => {
  editor.value?.destroy();
})

const isEditable = ref<boolean>(true);
function disableEditor() {
  isEditable.value = false;
  console.log('disableEditor', isEditable.value);
  editor.value?.setOptions({
    editable: false,
  });
  save()
}

function enableEditor() {
  isEditable.value = true;
  editor.value?.setOptions({
    editable: true,
  })
  editor.value?.commands.focus('end')
}

function switchEditor() {
  if (!editor.value) {
    return
  }

  if (editor.value.isEditable) {
    disableEditor()
    save()
  } else {
    enableEditor()
  }
}
const emits = defineEmits<{
  save: [content: string]
  focus: [];
}>()

function save() {
  if (!editor.value) {
    return
  }
  emits('save', editor.value.getHTML())
}
function setContent(content: string) {
  if (!editor.value) {
    return
  }
  console.log('content', content)
  editor.value.commands.setContent(content, false)
}
defineExpose<EditorTiptapInstance>({
  getContent: () => editor.value!.getHTML(),
  setContent,
  disableEditor,
  enableEditor,
  switchEditor,
  exportHtml: () => editor.value!.getHTML(),
})
</script>

<template>
  <div style="height: 100%;width: 100%;"
       class="u-tiptap-editor"
       v-if="editor">
    <drag-handle :editor="editor">
      <div class="custom-drag-handle" />
    </drag-handle>
    <TextBubbleMenu :editor="editor" />
    <div v-show="!hideToolBar && isEditable" class="control-group">
      <div class="button-group">
        <a-tooltip content="撤回" mini>
          <div
            class="button"
            :class="{ 'is-disabled': !editor.can().chain().focus().undo().run() }"
            @click="editor.chain().focus().undo().run()"
          >
            <iconpark-icon name="return" />
          </div>
        </a-tooltip>
        <a-tooltip content="重做" mini>
          <div
            class="button"
            :class="{ 'is-disabled': !editor.can().chain().focus().redo().run() }"
            @click="editor.chain().focus().redo().run()"
          >
            <iconpark-icon name="go-on"></iconpark-icon>
          </div>
        </a-tooltip>
        <a-divider direction="vertical" style="margin: 0 4px"></a-divider>
        <HeadlineSelect :editor="editor" />
        <div
          class="button"
          :class="{
            'is-active': editor.isActive('bold'),
            'is-disabled': !editor.can().chain().focus().toggleBold().run(),
          }"
          @click="editor.chain().focus().toggleBold().run()"
        >
          <iconpark-icon name="text-bold"></iconpark-icon>
        </div>
        <div
          class="button"
          :class="{
            'is-active': editor.isActive('italic'),
            'is-disabled': !editor.can().chain().focus().toggleItalic().run(),
          }"
          @click="editor.chain().focus().toggleItalic().run()"
        >
          <iconpark-icon name="text-italic"></iconpark-icon>
        </div>
        <div
          class="button"
          :class="{
            'is-active': editor.isActive('underline'),
            'is-disabled': !(editor.can().chain().focus() as any).toggleUnderline().run(),
          }"
          @click="(editor.chain().focus() as any).toggleUnderline().run()"
        >
          <iconpark-icon name="text-underline"></iconpark-icon>
        </div>
        <div
          class="button"
          :class="{
            'is-active': editor.isActive('strike'),
            'is-disabled': !editor.can().chain().focus().toggleStrike().run(),
          }"
          @click="editor.chain().focus().toggleStrike().run()"
        >
          <iconpark-icon name="strikethrough"></iconpark-icon>
        </div>
        <div class="button" :class="{
            'is-active': editor.isActive('codeBlock'),
          }"
             @click="editor.chain().focus().toggleCodeBlock().run()">
          <a-tooltip content="代码块" mini>
            <iconpark-icon name="code"></iconpark-icon>
          </a-tooltip>
        </div>
        <div
          class="button"
          @click="editor.chain().focus().toggleHighlight().run()"
          :class="{ 'is-active': editor.isActive('highlight') }"
        >
          <a-tooltip content="高亮笔" mini>
            <iconpark-icon name="high-light"></iconpark-icon>
          </a-tooltip>
        </div>
        <a-divider direction="vertical" style="margin: 0 4px"></a-divider>
        <LinkTool :editor="editor"></LinkTool>
        <div
          class="button"
          :class="{
            'is-disabled': editor.isActive('table'),
          }"
          @click="
            (editor.chain().focus() as any)
              .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
              .run()
          "
        >
          <iconpark-icon name="form"></iconpark-icon>
        </div>
      </div>
    </div>
    <a-scrollbar style="overflow-y: auto" :style="{ maxHeight: editorContentMaxHeight }">
      <editor-content style="height: 100%" :editor="editor" />
    </a-scrollbar>
  </div>
</template>

<style scoped lang="less">
/* 覆盖默认的黄色边框 */
:deep(.ProseMirror) {
  outline: none; /* 移除默认outline */
  &:focus {
    /* 或完全移除边框 */
    border: none;
  }
}
</style>

<style lang="less">
::selection {
  background-color: #b4d7ff !important;
}
// 工具按钮组样式
.control-group {
  padding: 4px;
  background-color: var(--color-bg-5);
  border-bottom: 1px solid var(--color-neutral-2);
  display: flex;
  align-items: center;
  .button-group {
    display: flex;
    align-items: center;
    gap: 4px;
    .button {
      width: 24px;
      height: 24px;
      font-size: 16px;
      border-radius: var(--border-radius-small);
      text-align: center;
      cursor: pointer;
      color: var(--color-neutral-8);
      &:hover {
        background-color: var(--color-neutral-3);
      }
      &.is-active {
        background-color: var(--color-neutral-3);
      }
      &.is-disabled {
        cursor: not-allowed;
        color: var(--color-neutral-5);
        pointer-events: none;
      }
    }
  }
}

.bubble-menu {
  border-radius: var(--border-radius-small) !important;
  background: var(--utools-background) !important;
  overflow: hidden;
}
.tippy-content {
  box-shadow: none;
  outline: none;
  border-radius: none;
}
/* Tippy样式 */
.tippy-box {
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
  font-size: 14px;
  background-color: white !important;
  color: #333 !important;
  z-index: 9999 !important;
}

.tippy-arrow {
  color: white !important;
}

.tiptap {
  height: 100%;
  padding: 4px;
  color: var(--text-color);
  /* Heading styles */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 1.1;
    margin-top: 1.2rem;
    text-wrap: pretty;
    text-align: inherit;
  }
  h3,
  h4,
  h5,
  h6 {
    margin-bottom: 8px;
  }
  h1 {
    margin-top: 16px;
    margin-bottom: 9px;
    font-size: 20pt;
  }
  h2 {
    margin-top: 15px;
    margin-bottom: 9px;
    font-size: 16pt;
  }
  h3 {
    font-size: 14pt;
    margin-top: 16px;
  }
  h4 {
    font-size: 12pt;
    margin-top: 12px;
  }
  h5 {
    font-size: 11pt;
    margin-top: 12px;
  }
  h6 {
    font-size: 11pt;
    margin-top: 12px;
  }
  p {
    font-size: 12pt;
    line-height: 1.7;
  }
  /* List styles */
  ul,
  ol {
    padding: 0 1rem;
    margin: 1.25rem 1rem 1.25rem 0.4rem;

    li p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  /* Code and preformatted text styles */
  code {
    background-color: #F7F3FF;
    border-radius: 0.4rem;
    color: var(--text-color);
    font-size: 0.85rem;
    padding: 0.25em 0.3em;
  }

  /* Code and preformatted text styles */
  pre {
    background: #19191D;
    border-radius: 0.5rem;
    color: #ffffff;
    margin: 1.2rem 0;
    padding: 0.75rem 1rem;
    code {
      background: none;
      color: inherit;
      font-size: 0.85rem;
      padding: 0;
    }
  }

  blockquote {
    border-left: 3px solid var(--gray-3);
    margin: 1.5rem 0;
    padding-left: 1rem;
  }


  /* Task list specific styles */
  ul[data-type='taskList'] {
    list-style: none;
    margin-left: 0;
    padding: 0;
    li {
      display: flex;
      align-items: center;
      > label {
        flex: 0 0 auto;
        margin-right: 0.5rem;
        user-select: none;
        display: flex;
        align-items: center;
      }

      > div {
        flex: 1 1 auto;
      }
    }

    input[type='checkbox'] {
      cursor: pointer;
    }

    ul[data-type='taskList'] {
      margin: 0;
    }
  }

  .table-toolbar {
    position: relative;
    z-index: 9999;
  }
  /* Table-specific styling */
  table {
    border-collapse: collapse;
    margin: 0;
    overflow: hidden;
    table-layout: fixed;
    width: 100%;
    td,
    th {
      border: 1px solid var(--color-neutral-3);
      box-sizing: border-box;
      min-width: 1em;
      padding: 6px 8px;
      position: relative;
      vertical-align: top;
      > * {
        margin-bottom: 0;
      }
    }

    th {
      background-color: var(--color-neutral-2);
      font-weight: bold;
      text-align: left;
    }

    .selectedCell:after {
      background: var(--color-neutral-4);
      opacity: 0.25;
      content: '';
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      pointer-events: none;
      position: absolute;
    }

    .column-resize-handle {
      background-color: red;
      bottom: -2px;
      pointer-events: none;
      position: relative;
      right: -2px;
      top: 0;
      width: 1000px;
      z-index: 99999;
    }
  }

  .tableWrapper {
    margin: 1.5rem 0;
    overflow-x: auto;
  }

  &.resize-cursor {
    cursor: ew-resize;
    cursor: col-resize;
  }

  /* 链接悬浮样式 */
  .custom-link,
  a {
    color: #4080ff;
    text-decoration: none;
    transition: all 100ms linear;
    padding: 0 2px;
    border-radius: 2px;
    cursor: pointer;
    &:hover {
      background-color: var(--color-neutral-2) !important;
      border-bottom: 1px dashed rgb(var(--arcoblue-6));
    }
  }

  /* Placeholder (at the top) */
  p.is-editor-empty:first-child::before {
    color: #adb5bd;
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
    font-size: 15px;
  }
}
</style>
