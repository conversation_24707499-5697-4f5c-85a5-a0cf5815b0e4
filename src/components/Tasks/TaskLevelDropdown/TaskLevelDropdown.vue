<script setup lang="ts">
import TaskConstants from "@/constants/tasks/TaskConstants.ts";
import { computed, ref, watch } from 'vue';

const props = defineProps<{
  popupContainer?: string | HTMLElement | undefined;
}>();

const modalValue = defineModel('modalValue');


const priority = computed<Record<string, any>>(() => {
  return TaskConstants.PRIORITY_SELECT_OPTIONS_TAG.reduce((obj: Record<string, any>, cur: any) => {
    obj[cur.value] = cur;
    return obj;
  }, {});
});

const emit = defineEmits<{
  blur: [];
}>();

function handleSelectEvent(value: string) {
  modalValue.value = value;
  emit('blur');
}
</script>

<template>
  <a-dropdown trigger="hover"
              class="min-dropdown-select"
              :popup-container="popupContainer">
    <a-link class="task-level">
      <iconpark-icon name="mark"
                     :color="priority[modalValue as any].color"
                     style="font-size: 16px; font-weight: 600;" />
    </a-link>
    <template #content>
      <div class="min-dropdown-select-options">
        <a-doption v-for="taskLevel in TaskConstants.PRIORITY_SELECT_OPTIONS_TAG"
                   :key="taskLevel.value as string"
                   @click="handleSelectEvent(taskLevel.value as string)">
          <iconpark-icon name="mark"
                         :color="taskLevel.color"
                         style="font-size: 16px;" />
        </a-doption>
      </div>
    </template>
  </a-dropdown>
</template>

<style scoped lang="less">

</style>
