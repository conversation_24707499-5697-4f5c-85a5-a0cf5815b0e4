<script setup lang="ts">
import TaskConstants from "@/constants/tasks/TaskConstants.ts";
import {computed, ref} from "vue";


const modalValue = defineModel<string[]>('modalValue', {
  default: () => []
});

const allValue = computed(() => !modalValue.value || modalValue.value.length == 0);

function handleSelectAll() {
  modalValue.value = [];
}
</script>

<template>
  <a-radio :model-value="allValue" @click="handleSelectAll">全部</a-radio>
  <a-divider direction="vertical" style="margin: 0 6px;"></a-divider>
  <a-checkbox-group v-model:model-value="modalValue">
    <a-checkbox v-for="(item) in TaskConstants.PRIORITY_SELECT_OPTIONS_TAG"
                :key="item.value"
                :value="item.value">
      <div class="u-fx u-fac u-gap5">
        <iconpark-icon name="mark" :style="{ color: item.color }"></iconpark-icon>
        <div :style="{ color: item.tagColor }">{{ item.label }}</div>
      </div>
    </a-checkbox>
  </a-checkbox-group>
</template>

<style scoped lang="less">

</style>
