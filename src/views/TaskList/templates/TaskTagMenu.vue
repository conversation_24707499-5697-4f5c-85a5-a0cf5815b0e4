<script setup lang="ts">
import { h, nextTick, onMounted, ref, useTemplateRef } from 'vue';
import type { ITaskTag } from '@xiaou66/todo-plugin';
import { ExtensionManager } from '@/extension';
import type { IMenuItem } from '@/views/TaskList/templates/Menu.ts';
import { useEventListener } from '@vueuse/core';
import { Modal } from '@arco-design/web-vue';
import TaskTagSaveModal from '@/views/TaskList/templates/TaskTagMenu/TaskTagSaveModal.vue';
import type { TaskTagSaveModalInstance } from '@/views/TaskList/templates/TaskTagMenu/TaskTagSaveModal.ts';
import { useCollapse } from '@/hooks/useCollapse.ts';
import { FlowExecutor } from '@/views/CollectScene/flow';

const tagListData = ref<ITaskTag[]>();

const props = defineProps<{
  setCategoryList: (menuItems: IMenuItem[]) => void
}>();


const active = defineModel<string>('active');
const tagStatistics = ref<{
  [key: string]: number;
}>({});

function handleStatisticsCategoriesCount() {
  ExtensionManager.getTaskInstance().listTask({})
    .then((data) => {
      const list = data.list;
      // 统计标签数量
      tagStatistics.value = list.reduce((acc, cur) => {
        for (const tagName of cur.tagNameList || []) {
          if (acc[tagName]) {
            acc[tagName] += 1;
          } else {
            acc[tagName] = 1;
          }
        }
        return acc;
      }, {} as Record<string, number>);
    });
}

async function request() {
  const taskTagInstance = ExtensionManager.getTaskTagInstance();
  tagListData.value = await taskTagInstance.listTag();
  props.setCategoryList(tagListData.value.map(item => {
    return {
      id: item.id,
      name: item.name,
      desc: '',
      menuType: 'tags',
      searchParams: {
        tagNameList: [ item.name ]
      }
    } as IMenuItem
  }));
  nextTick(() => {
    calcCollapseHeight()
  });
  handleStatisticsCategoriesCount();
}
onMounted(() => {
  request()
});
useEventListener(window, 'taskList::refreshAll', () => {
  if (!collapsed.value) {
    request();
  }
});

function handleDeleteTag(tag: ITaskTag) {
  Modal.confirm({
    title: '移除标签提醒',
    content: () => h('div', {}, [
      h('div', {}, `在所有任务中移除「${tag.name}」标签使用`),
      h('div', {  }, [
        h('span', { style: 'color: #f5222d' }, `此操作`),
        h('span', {style: 'font-weight: bold; color: #f5222d'}, '无法还原')
      ])
    ]),
    cancelText: '放弃',
    okText: '确认移除',
    okButtonProps: {
      status: 'danger'
    },
    onOk: async () => {
      await ExtensionManager.getTaskTagInstance().deleteTag(tag.name);
      await request();
      await FlowExecutor.adjustFlowNodeData();
      if (active.value === tag.id) {
        // 当前是删除的分组
        active.value = 'toDay';
      }
    }
  });
}
function handleCreateAfter(tagId: string) {
  active.value = tagId;
  request();
  collapsed.value = false;
}
const [collapsed, handleCollapse, calcCollapseHeight] = useCollapse();
const taskTagSaveModalRef = useTemplateRef<TaskTagSaveModalInstance>('taskTagSaveModalRef');
</script>

<template>
  <TaskTagSaveModal ref="taskTagSaveModalRef"
                    @create="handleCreateAfter" />
  <!--  分组  -->
  <div class="sidebar-group">
    <div class="sidebar-section">
      <div class="section-title"
           @click="handleCollapse">
        <div class="u-fx u-fac u-pos-rel" >
          <iconpark-icon :name="collapsed ? 'right' : 'down'"
                         :size="15"
                         style="position:relative; left: -2px;"></iconpark-icon>
          <div>标签</div>
        </div>
        <div class="u-fx u-fac">
          <iconpark-icon name="plus"
                         @click.stop="taskTagSaveModalRef?.show()"></iconpark-icon>
        </div>
      </div>
    </div>

    <div class="sidebar-section item"
         :class="{ collapsed }">
      <div  ref="collapseRef">
        <a-dropdown v-for="tag in tagListData"
                  :key="tag.id"
                  class="u-transparent min-dropdown-select"
                  trigger="contextMenu"
                  alignPoint>
        <div class="sidebar-item"
             :class="{ active: active === tag.id }"
             @click="active = tag.id">
          <div class="name">{{ tag.name }}</div>
          <div class="count">{{ tagStatistics[tag.name] || 0 }}</div>
        </div>
        <template #content>
          <div class="min-dropdown-select-options">
<!--            <a-doption @click="groupBoxSaveDrawerRef?.show(group)">
              <template #icon>
                <iconpark-icon name="write"
                               style="color: var(&#45;&#45;color-neutral-8)" />
              </template>
              编辑
            </a-doption>-->
<!--            <a-divider style="margin: 4px 0; padding: 0"></a-divider>-->
            <a-doption @click="handleDeleteTag(tag)">
              <template #icon>
                <iconpark-icon name="delete"
                               style="color: var(--color-neutral-8)" />
              </template>
              移除
            </a-doption>
          </div>
        </template>
      </a-dropdown>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
