<script lang="ts" setup>
import {computed, onMounted, ref} from "vue";
import {ExtensionManager} from "@/extension";
import type { IGroupInfo } from "@xiaou66/todo-plugin";

const options = ref<{label:string, value: string}[]>([]);
const optionMap = computed<Record<string, any>>(() => {
  return options.value.reduce((obj, cur) => {
    // @ts-ignore
    obj[cur.value] = cur;
    return obj;
  }, {});
});

async function groupList() {
  const taskInstance = ExtensionManager.getGroupInstance();
  const group = await taskInstance.listGroup({} as any);
  options.value = group.list.map((item: IGroupInfo) => ({
    label: item.groupName,
    value: item.groupId,
  }));
}
onMounted(() => {
  groupList();
});

const modelValue = defineModel<string>('modelValue');

const emits = defineEmits<{
  (e: 'change', value: string): void
}>();

function handleSelectGroup(value: string) {
  modelValue.value = value;
  emits('change', value);
}
</script>
<template>
  <a-dropdown class="min-dropdown-select"
              @select="(value: any) => handleSelectGroup(value)">
    <a-button class="u-transparent"
              size="mini"
              style="margin-right: 10px;">
      {{ modelValue && modelValue != 'collectBox' && optionMap[modelValue] ? optionMap[modelValue].label : '未分组' }}
    </a-button>
    <template #content>
      <div class="min-dropdown-select-options">
        <a-doption value="collectBox">未分组</a-doption>
        <a-doption v-for="(option) in options"
                   :key="option.value"
                   :value="option.value">
          {{ option.label }}
        </a-doption>
      </div>
    </template>
  </a-dropdown>
</template>
<style lang="less" scoped>
.arco-btn {
  border-radius: var(--border-radius-medium) !important;
  overflow: hidden;
  background-color: transparent;
  transition: all 300ms linear;
  &:hover {
    background-color: var(--color-secondary-hover);
  }
}
</style>
