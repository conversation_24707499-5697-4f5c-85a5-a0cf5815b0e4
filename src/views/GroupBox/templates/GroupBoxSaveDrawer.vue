<script setup lang="ts">
import { ref, toRaw, useTemplateRef } from 'vue';
import type { GroupBoxSaveDrawerInstance } from './GroupBoxSaveDrawerInstance.ts'
import { ImageCropper, type ImageCropperInstance } from '@/components/image'
import type { FormInstance } from '@arco-design/web-vue';
import  { Notification } from '@arco-design/web-vue';
import type { GroupExtension, IGroupCreateParams, IGroupInfo } from '@xiaou66/todo-plugin'
import { cloneDeep, merge } from 'es-toolkit';
import { ExtensionManager } from '@/extension'
import { useEscModal } from '@/hooks/useModal.ts';

const visible = ref(false);


const defaultValue: IGroupCreateParams | IGroupInfo = {
  groupName: '',
  code: 'utools',
  utoolsKey: false,
};
const { startEsc, stopEsc } = useEscModal();
const form = ref<IGroupCreateParams | IGroupInfo>(cloneDeep(defaultValue) as IGroupCreateParams)
const formRef = useTemplateRef<FormInstance>('formRef');
function handleUploadImage() {
  const paths = utools.showOpenDialog({
    title: '选择封面文件',
    filters: [{ 'name': 'image', extensions: ['png', 'jpeg', 'jpg'] }]
  });
  if (!paths || paths.length === 0) {
    return;
  }

  const path = paths[0];
  const stats = window.fs.statSync(path);
  if (stats.size >= 1024 * 1024 * 2) {
    Notification.warning({
      title: '图片限制温馨提示',
      content: '为了加载速度请选择图片不能超过 2MB'
    });
    return;
  }
  imageCropperModalInstance.value?.show('file://' + path);
}

function handleSaveImage(base64: string) {
  form.value.cover = base64;
}


const emits = defineEmits<{
  saveAfter: [groupId: string];
}>();

async function handleBeforeOk(close?: boolean) {
  const error = await formRef.value.validate();
  if (error) {
    return false;
  }
  const instance = ExtensionManager.getGroupInstance();
  let groupId = (form.value as IGroupInfo).groupId;
  if (groupId) {
    console.log(form.value);
    await instance.saveGroup(toRaw(form.value) as IGroupInfo)
  } else {
    groupId = await instance.createGroup(toRaw(form.value));
  }
  console.log('groupId', groupId);
  emits('saveAfter', groupId);
  if (close) {
    visible.value = false;
    stopEsc();
  }
  return true;
}

function show(groupInfo?: IGroupInfo) {
  form.value = merge(cloneDeep(defaultValue), groupInfo || {}) as IGroupInfo;
  console.log('show', form.value);
  visible.value = true
  startEsc();
}
defineExpose<GroupBoxSaveDrawerInstance>({
  show,
});

const imageCropperModalInstance = useTemplateRef<ImageCropperInstance>('imageCropperModalRef');
</script>

<template>
<!--  <ImageCropper ref="imageCropperModalRef" @saveOk="handleSaveImage" />-->
  <a-modal title="保存分组"
           v-model:visible="visible"
           :width="500"
           :top="100"
           :alignCenter="false"
           @before-ok="handleBeforeOk"
           @close="stopEsc"
           unmountOnClose>
    <div style="padding: 10px">
      <a-form ref="formRef" :model="form" auto-label-width>
        <a-form-item label="分组名称"
                     field="groupName"
                     :rules="[{ required: true, message: '请输入分组名称' }]">
          <a-input v-model:model-value="form.groupName"
                   size="small"
                   :max-length="10"
                   placeholder="给分组取个名称"
                   @pressEnter="handleBeforeOk(true)"
                   show-word-limit>
          </a-input>
        </a-form-item>
<!--        <a-form-item label="分组图标">
          <div class="cover-wrapper u-fac">
            <div class="cover">
              <div class="action"
                   @click="handleUploadImage">
                <div style="display: flex; justify-content: center; flex-direction: column;">
                  <iconpark-icon name="upload-picture"></iconpark-icon>
                </div>
              </div>
              <img :src="form.cover" alt="" />
            </div>
            <a-scrollbar style="height:75px;overflow: auto;">
              <div class="cover-select">
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
                <div class="min-cover">
                  <img src="https://s2.loli.net/2023/04/15/k4IbQGzMZ9v6fYN.jpg">
                </div>
              </div>
            </a-scrollbar>
          </div>
        </a-form-item>-->
<!--        <a-form-item label="全局指令">
          <template #extra>
            开启后可以直接在 uTools 输入框中输入分组名直接进入分组
          </template>
          <a-switch v-model:model-value="form.utoolsKey"
                    checked-color="#2ecc71"
                    style="width: 46px">
          </a-switch>
        </a-form-item>-->
      </a-form>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
.cover-wrapper {
  display: flex;
  gap: 10px;
  .action {
    position: absolute;
    background: rgba(0, 0, 0, .2);
    width: 100%;
    height: 100%;
    border-radius: 10px;
    opacity: 0;
    transition: opacity 250ms ease;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    iconpark-icon {
      font-size: 32px;
    }
  }
  .cover {
    position: relative;
    width: 64px;
    height: 64px;
    flex-shrink: 0;
    border-radius: 10px;
    &:hover .action {
      opacity: 1;
    }
    img {
      object-fit: cover;
      border-radius: 10px;
    }
  }
  .min-cover,.cover {
    img {
      object-fit: cover;
      border-radius: 10px;
    }
  }
  .min-cover {
    min-width: 32px;
    min-height: 32px;
    width: 32px;
    height: 32px;
  }
  .cover-select {
    display: flex;
    flex-wrap: wrap;
    gap: 5px 10px;
  }
}
</style>
