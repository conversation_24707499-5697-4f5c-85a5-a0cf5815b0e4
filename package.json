{"name": "u-todo-pro", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite --mode dev", "dev:prod": "vite --mode prod", "dev:build": "vite build --mode prod", "build:test": "vite build --mode test --config vite.config.release.ts", "build:release": "vite build --mode release --config vite.config.release.ts", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.7.4", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.1.0", "@fullcalendar/core": "^6.1.19", "@fullcalendar/daygrid": "^6.1.19", "@fullcalendar/interaction": "^6.1.19", "@fullcalendar/vue3": "^6.1.19", "@tiptap/core": "^3.2.0", "@tiptap/extension-code-block-lowlight": "^3.2.0", "@tiptap/extension-drag-handle": "^3.2.0", "@tiptap/extension-drag-handle-vue-3": "^3.2.0", "@tiptap/extension-gapcursor": "^3.2.0", "@tiptap/extension-highlight": "^3.2.0", "@tiptap/extension-image": "^3.2.0", "@tiptap/extension-link": "^3.2.0", "@tiptap/extension-placeholder": "^3.2.0", "@tiptap/extension-table": "^3.2.0", "@tiptap/extension-table-cell": "^3.2.0", "@tiptap/extension-table-header": "^3.2.0", "@tiptap/extension-table-of-contents": "^3.2.0", "@tiptap/extension-table-row": "^3.2.0", "@tiptap/extension-task-item": "^3.2.0", "@tiptap/extension-task-list": "^3.2.0", "@tiptap/extension-underline": "^3.2.0", "@tiptap/extension-unique-id": "^3.2.0", "@tiptap/pm": "^3.2.0", "@tiptap/starter-kit": "^3.2.0", "@tiptap/suggestion": "^3.2.0", "@tiptap/vue-3": "^3.2.0", "@vueuse/core": "^13.6.0", "@xiaou66/todo-plugin": "workspace:^", "dayjs": "^1.11.13", "driver.js": "^1.3.6", "es-toolkit": "^1.39.10", "hotkeys-js": "^3.13.15", "html-to-md": "^0.8.8", "html-to-text": "^9.0.5", "immutability-helper": "^3.1.1", "lowlight": "^3.3.0", "lunar-calendar": "^0.1.4", "markdown-it": "^14.1.0", "nanoid": "^5.1.5", "pinia": "^3.0.3", "tippy.js": "^6.3.7", "vue": "^3.5.18", "vue-cropper": "^1.1.4", "vue-router": "^4.5.1"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@iconify/utils": "^3.0.1", "@prettier/plugin-oxc": "latest", "@tdesign-vue-next/auto-import-resolver": "^0.1.1", "@tsconfig/node22": "^22.0.2", "@types/html-to-text": "^9.0.4", "@types/jsdom": "^21.1.7", "@types/markdown-it": "^14.1.2", "@types/node": "^22.16.5", "@unocss/preset-icons": "^66.4.2", "@unocss/transformer-directives": "^66.4.2", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "@xiaou66/u-utools": "^0.0.5", "@xiaou66/u-web-ui": "0.0.61", "code-inspector-plugin": "^1.2.0", "electron": "22.3.27", "eslint": "^9.31.0", "eslint-plugin-oxlint": "~1.8.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "less": "^4.4.0", "npm-run-all2": "^8.0.4", "oxlint": "~1.8.0", "prettier": "3.6.2", "prosemirror-state": "^1.4.3", "tdesign-vue-next": "^1.15.4", "typescript": "~5.8.0", "unocss": "^66.4.2", "unplugin-auto-import": "^20.0.0", "unplugin-vue-components": "^29.0.0", "utools-api-types": "^7.2.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^8.0.0", "vitest": "^3.2.4", "vue-tsc": "^3.0.4"}}