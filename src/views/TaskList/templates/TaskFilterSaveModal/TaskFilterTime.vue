<script setup lang="ts">
import { TASK_FILTER_TIME_RULES } from './TaskFilterRules.ts';
import { ref, computed } from 'vue';
const dynamicTimeType = defineModel<string[]>('dynamicTimeType', {
  default: () => [],
  set(v) {
    if (!v.includes('custom')) {
      customDiffTime.value = [];
    }
    return v;
  },
});
const customDiffTime = defineModel<number[]>('customDiffTime', {
  default: () => []
});

const isCustom = computed(() => dynamicTimeType.value.includes('custom'));

function inputAutoFocus(e:PointerEvent) {
  if (e.target instanceof HTMLInputElement) {
    e.target.focus();
  }
  return e;
}
</script>

<template>
 <div style="width: 100%">
   <div v-if="isCustom" class="u-mb10">
     <a-input-group size="mini">
       <a-input v-model:model-value="customDiffTime[0]"
                @click.stop="inputAutoFocus" >
         <template #prefix>前</template>
         <template #suffix>天</template>
       </a-input>
       <a-input v-model:model-value="customDiffTime[1]"
                @click.stop="inputAutoFocus">
         <template #prefix>后</template>
         <template #suffix>天</template>
       </a-input>
     </a-input-group>
   </div>
   <a-select v-model:model-value="dynamicTimeType"
             placeholder="默认所有" multiple allow-clear>
     <a-option v-for="(rule) in TASK_FILTER_TIME_RULES"
               :key="rule.code"
               :value="rule.code">
       <span>{{ rule.label }}</span>
     </a-option>
   </a-select>
 </div>
</template>

<style scoped lang="less">

</style>
