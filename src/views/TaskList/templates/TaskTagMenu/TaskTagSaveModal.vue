<script setup lang="ts">
import type { TaskTagSaveModalInstance } from './TaskTagSaveModal.ts';
import { ref, useTemplateRef } from 'vue';
import type { ITaskTagCreateParam } from '@xiaou66/todo-plugin';
import type { FormInstance } from '@arco-design/web-vue';
import { ExtensionManager } from '@/extension';
import { useEscModal } from '@/hooks/useModal.ts';

const visible = defineModel<boolean>('visible');
const { startEsc, stopEsc } = useEscModal();
function show() {
  form.value.name = '';
  visible.value = true;
  startEsc();
}
function hide() {
  stopEsc();
}
const formRef = useTemplateRef<FormInstance>('formRef');
const form = ref<ITaskTagCreateParam>({
  name: '',
});
const emits = defineEmits<{
  create: [id: string]
}>();

async function handleBeforeOk(close?: boolean) {
  const error = await formRef.value.validate();
  if (error) {
    return false;
  }
  const taskTag = await ExtensionManager.getTaskTagInstance().createTag({
    ...form.value,
  });
  if (close) {
    visible.value = false;
    stopEsc();
  }
  emits('create', taskTag.id);
}
defineExpose<TaskTagSaveModalInstance>({
  show,
})
</script>

<template>
  <a-modal v-model:visible="visible"
           title="保存标签"
           ok-text="保存"
           :top="100"
           :alignCenter="false"
           @before-ok="handleBeforeOk"
           @close="hide"
           unmountOnClose>
    <a-form ref="formRef"
            :model="form">
      <a-form-item label="标签名称"
                   field="name"
                   :rules="[{required: true, message: '请输入标签名称'}]">
        <a-input v-model:model-value="form.name"
                 @pressEnter="handleBeforeOk(true)"></a-input>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less">

</style>
