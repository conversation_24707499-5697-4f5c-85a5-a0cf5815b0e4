<script setup lang="ts">
import { LeftMenuSplitPanel } from '@/components/common';
import CollectSceneDetail from './templates/CollectSceneDetail/CollectSceneDetail.vue';
import { onMounted, ref } from 'vue';
import type { IFlowItem } from '@xiaou66/todo-plugin';
import { ExtensionManager } from '@/extension';
import { Message } from '@arco-design/web-vue';
import { FLOW_NODE_ACTIONS_MAP } from '@/views/CollectScene/flow/node';

const flowNodeList = ref<IFlowItem[]>([]);
async function requestList() {
  flowNodeList.value =  await ExtensionManager.getFlowInstance().getFlowList();
}

const activeFlowId = ref('');
onMounted(async () => {
  await requestList();
  if (flowNodeList.value && flowNodeList.value.length > 0) {
    activeFlowId.value = flowNodeList.value[0].id;
  }
});
function addFlowItem() {
  const isNew = !!flowNodeList.value.filter(item => item.id === '').length;
  if (isNew) {
    Message.warning('存在未保存的场景, 请先保存');
    activeFlowId.value =  '';
    return
  }
  flowNodeList.value.unshift({
    id: '',
    title: '待创建场景',
  } as any);
  activeFlowId.value =  '';
}
function handleDeleteFlow(flowItem: IFlowItem) {
  if (flowItem.id) {
    for (const node of flowItem.nodeList) {
      const destroy = FLOW_NODE_ACTIONS_MAP[node.nodeCode].onDestroy;
      if (destroy) {
        destroy(flowItem, node);
      }
    }
    ExtensionManager.getFlowInstance().deleteFlow(flowItem.id);
    requestList().then(() => {
      if (flowItem.id === activeFlowId.value && flowNodeList.value.length) {
        activeFlowId.value = flowNodeList.value[0].id;
      }
    })
  } else {
    const index = flowNodeList.value.findIndex(item => item.id === flowItem.id);
    if (index !== -1) {
      flowNodeList.value.splice(index, 1);
    }
  }
}
</script>

<template>
  <a-page-header :show-back="false"
                 title="场景"
                 subtitle="从此，任务不再迷路" style="padding: 10px 0" />
  <div id="collect-scene-main"
       style="height: calc(100vh - 94px);">
    <LeftMenuSplitPanel>
      <template #left>
        <div class="container">
          <div>
            <a-button class="u-w-full u-transparent"
                      size="mini"
                      @click="addFlowItem">
              <template #icon>
                <iconpark-icon style="padding-top: 5px;" :size="14" name="plus" />
              </template>
              添加
            </a-button>
          </div>
          <div class="menu-sub">

            <div v-for="flowItem in flowNodeList" :key="flowItem.id">
              <a-dropdown class="min-dropdown-select" trigger="contextMenu" alignPoint :style="{display:'block'}">
                <div class="u-fx u-fac u-gap10 menu-item"
                     :class="{'active': flowItem.id === activeFlowId}"
                     @click="activeFlowId = flowItem.id">
                  {{ flowItem.title }}
                </div>
                <template #content>
                  <div class="min-dropdown-select-options">
                    <a-doption @click="handleDeleteFlow(flowItem)">
                      <template #icon>
                        <iconpark-icon name="delete"
                                       style="color: var(--color-neutral-8)" />
                      </template>
                      删除
                    </a-doption>
                  </div>
                </template>
              </a-dropdown>
            </div>

          </div>
        </div>
      </template>
      <template #content>
        <CollectSceneDetail :flowId="activeFlowId"
                            @refresh="requestList" />
      </template>
    </LeftMenuSplitPanel>
  </div>
</template>

<style scoped lang="less">
body[arco-theme="dark"] {
  .sidebar {
    background: var(--color-neutral-2);
  }
  .menu-item {
    &:hover {
      background-color: rgba(72, 72, 73, 0.55);
    }
  }
}
// 手势菜单
.menu-sub {
  padding: 2px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  .menu-item {
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    iconpark-icon {
      font-size: 16px;
    }
    &:hover {
      background-color: var(--color-neutral-2);
    }

    // 选中状态
    &.active {
      background-color: var(--color-neutral-3);
    }
  }
}
</style>
