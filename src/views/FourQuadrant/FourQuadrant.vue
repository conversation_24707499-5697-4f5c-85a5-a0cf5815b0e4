<script setup lang="ts">
import { computed, onMounted, provide, ref } from 'vue';
import type { TaskListItem } from '@xiaou66/todo-plugin';
import { ExtensionManager } from '@/extension';
import { groupBy } from 'es-toolkit';
import TaskConstants from "@/constants/tasks/TaskConstants.ts";
import {
  type ITaskViewProvide,
  TaskDragPreview,
  taskViewProvideCode, useSaveTaskItem,
  useTaskItemDrag
} from '@/components/Tasks';
import FourQuadrantPanel from "./templates/FourQuadrantPanel.vue";
import { useEventListener } from '@vueuse/core';



const taskList = ref<TaskListItem[]>([]);
const taskListTaskLevel = computed(() => {
  return groupBy(taskList.value, (item) => item.taskLevel);
});

const taskLevelSortList = ref(['0', '1', '2', '3']);
async function refreshData() {
  const res  = await ExtensionManager.getTaskInstance().listTask({})
  taskList.value = res.list;
}

useEventListener(window, 'taskList::refreshAll', () => {
  refreshData();
});

onMounted(() => {
  refreshData();
});

const saveTaskItem = useSaveTaskItem();
const taskItemDrag = useTaskItemDrag(taskList);
provide<ITaskViewProvide>(taskViewProvideCode, {
  refreshData,
  getScene() {
    return "default";
  },
  ...taskItemDrag,
  saveTask: saveTaskItem,
})
</script>

<template>
  <TaskDragPreview />
  <a-page-header :show-back="false"
                 title="四象限"
                 subtitle="一键四分，专注此刻该做的" style="padding: 10px 0" />
  <div class="four-quadrant">
    <FourQuadrantPanel v-for="taskLevel in taskLevelSortList"
                       :key="taskLevel"
                       :task-level-option="TaskConstants.PRIORITY_SELECT_OPTIONS_TAG_MAP[taskLevel]"
                       :task-list="taskListTaskLevel[taskLevel] || []"
    />
  </div>
</template>

<style scoped lang="less">
@media (max-width: 800px) {
  .four-quadrant {
    gap: 4px;
  }
}
.four-quadrant {
  padding: 10px;
  display: grid;
  gap: 1%;
  grid-template-rows: 50% 50%;
  grid-template-columns: 49% 49%;
  justify-content: center;
  height: calc(100% - 60px);
}
</style>
