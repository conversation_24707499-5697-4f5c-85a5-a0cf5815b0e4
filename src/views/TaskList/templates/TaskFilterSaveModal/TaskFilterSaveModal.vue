<script setup lang="ts">
import {ref, useTemplateRef} from "vue";
import type {TaskFilterSaveModalInstance} from "./TaskFilterSaveModal.ts";
import type {ITaskTag, IGroupInfo, SaveTaskFilterType, ITaskFilter} from "@xiaou66/todo-plugin";
import { useEscModal } from "@/hooks/useModal.ts";
import { ExtensionManager } from "@/extension";
import TaskFilterTime from './TaskFilterTime.vue';
import TaskFilterLevelSelect from "./TaskFilterLevelSelect.vue";
import type { FormInstance } from "@arco-design/web-vue";
import {cloneDeep, merge} from "es-toolkit";


const emits = defineEmits<{
  close: [id?: string]
}>();
const visible = ref(false);
const taskTagList = ref<ITaskTag[]>([]);
const groupList = ref<IGroupInfo[]>([]);
const formRef = useTemplateRef<FormInstance>('formRef')

const defaultFormData: SaveTaskFilterType = {
  filterName: '',
  dynamicTimeTypeList: [],
  customDiffTime: [],
  taskGroupIdList: [],
  tagNameList: [],
  taskLevelList: [],
  filterType: 'default',
}

const formData = ref<SaveTaskFilterType>(cloneDeep(defaultFormData));

const { startEsc, stopEsc } = useEscModal();

function show(data?: ITaskFilter) {
  startEsc();
  formData.value = merge(cloneDeep(defaultFormData), data || {});
  visible.value = true;
  ExtensionManager.getGroupInstance()
    .listGroup({})
    .then((res) => {
      groupList.value = res.list;
    });
  ExtensionManager.getTaskTagInstance()
    .listTag()
    .then((res) => {
      taskTagList.value = res;
    });
}

async function handleTaskFilterSave() {
  const error = await formRef.value?.validate()
  if (error) {
    return false;
  }
  await ExtensionManager.getTaskFilterInstance()
    .saveTaskFilter(formData.value);
  return true;
}
function handleCloseEvent() {
  stopEsc();
  emits('close', formData.value?.id)
}

defineExpose<TaskFilterSaveModalInstance>({
  show,
});


</script>

<template>
  <a-modal v-model:visible="visible"
           :top="100"
           :align-center="false"
           @close="handleCloseEvent"
           :on-before-ok="handleTaskFilterSave"
           :closable="false"
           unmount-on-close>
    <template #title>
      <div class="u-fx u-fac u-f-between u-w-full">
        <div>筛选器</div>
        <div>
<!--          <a-radio-group type="button">
            <a-radio>普通</a-radio>
            <a-radio>高级</a-radio>
          </a-radio-group>-->
        </div>
        <div style="width: 30px;"></div>
      </div>
    </template>
    <a-form ref="formRef" :model="formData" auto-label-width>
      <a-form-item label="名称"
                   field="filterName"
                   :rules="[{ required: true, message: '请取一个好听筛选器的名称' }]">
        <a-input v-model:model-value="formData.filterName"></a-input>
      </a-form-item>
      <a-form-item label="时间">
        <TaskFilterTime v-model:dynamic-time-type="formData.dynamicTimeTypeList"
                        v-model:custom-diff-time="formData.customDiffTime" />
      </a-form-item>
      <a-form-item label="分组">
        <a-select v-model:model-value="formData.taskGroupIdList"
                  placeholder="默认所有"
                  multiple
                  allow-clear>
            <a-option value="collectBox">未分组</a-option>
            <a-option v-for="group in groupList"
                      :key="group.groupId"
                      :value="group.groupId">
                {{ group.groupName }}
            </a-option>
        </a-select>
      </a-form-item>
      <a-form-item label="标签">
        <a-select v-model:model-value="formData.tagNameList"
                  placeholder="默认所有"
                  multiple
                  allow-clear>
            <a-option v-for="taskTag in taskTagList"
                      :key="taskTag.id"
                      :value="taskTag.name">
                {{ taskTag.name }}
            </a-option>
        </a-select>
      </a-form-item>
      <a-form-item label="优先级">
        <TaskFilterLevelSelect
          v-model:modal-value="formData.taskLevelList" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less">

</style>
