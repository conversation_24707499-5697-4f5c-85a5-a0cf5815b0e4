<script setup lang="ts">
import { useRouter } from 'vue-router';
import TaskSortDropdown from '../TaskSortDropdown/TaskSortDropdown.vue';
import { onMounted, provide, ref, toRaw, useTemplateRef, watch } from 'vue';
import { ExtensionManager } from '@/extension';
import type {
  IViewConfig,
  TaskListItem,
  TaskListParams,
} from '@xiaou66/todo-plugin';
import { TaskViewLayer } from '@/views/GroupDetail/templates';
import {
  useTaskItemDrag,
  useSaveTaskItem,
} from '@/components/Tasks';
import type { ITaskViewProvide, ITaskViewContext } from './index.ts';
import { taskViewProvideCode } from './index.ts';
import { Modal } from '@arco-design/web-vue';
import { useEventListener } from '@vueuse/core';
import { useAiTaskDrawer } from '@/components/ai';
import { cloneDeep } from 'es-toolkit';
import { vi } from 'vitest';
const props = withDefaults(
  defineProps<{
    id: string;
    type: 'group' | 'system' | 'filter' | 'tags';
    defaultViewConfig?: IViewConfig;
    title: string;
    subtitle: string;
    showBack?: boolean;
    searchParams?: TaskListParams;
    scene?: 'finish' | 'delete' | 'default';
  }>(),
  {
    showBack: false,
    searchParams: () => ({}) as TaskListParams,
    scene: 'default',
  },
);

const emits = defineEmits<{
  refresh: [];
}>();

const viewMode = ref<'list' | 'grid'>('list');
const viewConfig = ref<IViewConfig>({
  groupType: 'taskLevel',
  sortType: 'custom',
  showFinish: true
});

watch(() => viewConfig.value, (newValue, oldValue) => {
  viewConfigChange();
  if (newValue.groupType === 'taskStartDate'
    || oldValue.groupType === 'taskStartDate') {
    // 刷新需要刷新出已过期的任务或清理掉之前查询出过期的任务
    refreshData();
  }
}, { deep: true });
watch(() => viewMode.value, () => {
  viewConfigChange();
}, { deep: true });
async function viewConfigChange() {
  if (props.type === 'system') {
    ExtensionManager.getLocalStorageInstance().setData(
      `systemGroup/viewConfig/${props.id}`, cloneDeep(viewConfig.value),
    );
    ExtensionManager.getLocalStorageInstance().setData(
        `systemGroup/viewMode/${props.id}`, cloneDeep(viewMode.value));
  } else if (props.type === 'group') {
    await ExtensionManager.getGroupInstance().saveGroup({
      groupId: props.id,
      viewConfig: cloneDeep(viewConfig.value),
    });
    ExtensionManager.getLocalStorageInstance().setData(
        `group/viewMode/${props.id}`, cloneDeep(viewMode.value));
  }
}
async function loadViewConfig() {
  if (props.type === 'system') {
    viewMode.value = ExtensionManager.getLocalStorageInstance().getData(
        `systemGroup/viewMode/${props.id}`) || 'list';
  } else if (props.type === 'group'){
    viewMode.value = ExtensionManager.getLocalStorageInstance().getData(
        `group/viewMode/${props.id}`) || 'list';
  }
  if (props.defaultViewConfig) {
    console.log('viewConfig', props.defaultViewConfig);
    viewConfig.value = props.defaultViewConfig;
    return;
  }
  if (props.type === 'group') {
    const group = await ExtensionManager.getGroupInstance().getGroup(props.id);
    viewConfig.value = group?.viewConfig || {
      groupType: 'taskLevel',
      sortType: 'custom',
      showFinish: true
    };
  } else if (props.type === 'system') {
    viewConfig.value = ExtensionManager.getLocalStorageInstance().getData<IViewConfig>(
      `systemGroup/viewConfig/${props.id}`,
    ) || {
      groupType: 'taskLevel',
      sortType: 'custom',
      showFinish: true
    };
  }
}

const data = ref<TaskListItem[]>([]);
async function refreshData() {
  const searchParams = cloneDeep<TaskListParams>(props.searchParams);
  console.log('taskView-refreshData-taskList-start', searchParams);
  const taskInstance = ExtensionManager.getTaskInstance();
  const modifySearchParams: TaskListParams = {};
  debugger
  if (viewConfig.value.groupType === 'taskStartDate'
    && searchParams.taskTimeRange
    && searchParams.taskTimeRange.length === 2) {
    // 在时间分类出, 需要加载出全部已过期的任务
    // fix 这里开始不能设置为 0 因为后面检索的时候都是使用 !字段 判断的会导致结果不准确
    modifySearchParams.taskTimeRange =  [1, searchParams.taskTimeRange[1]]
  }
  console.log('taskview-refreshData', {
    ...searchParams,
    ...modifySearchParams,
  });
  taskInstance
    .listTask({
      ...searchParams,
      ...modifySearchParams,
    })
    .then((res) => {
      data.value = res.list;
      console.log('taskView-refreshData-taskList-end', data.value);
    });
  if (viewConfig.value.showFinish) {
    ExtensionManager.getTaskInstance().listTask({
      ...searchParams,
      searchType: 'finish',
    }).then(res => {
      finishData.value = res.list;
    })
  } else {
    finishData.value = [];
  }
  emits('refresh');
}

useEventListener(window, 'tasklist::taskView', () => {
  refreshData();
});
useEventListener(window, 'taskList::refreshAll', () => {
  refreshData();
});
onMounted(async () => {
  await loadViewConfig();
  loadContext();
  refreshData();
});

watch(
  () => [props.id, props.searchParams],
  async () => {
    debugger
    console.log('重新加载配置');
    await loadViewConfig();
    loadContext();
    console.log('viewConfig', viewConfig.value);
    refreshData();
  }
, { deep: true });

const router = useRouter();
function handleBack() {
  router.back();
}

const context = ref<ITaskViewContext>({});
// TODO 这里一定要优化一下，出错几率会增加
function loadContext() {
  const currentContext: any = {};
  if (props.type === 'group') {
    currentContext.groupId = props.id;
  } else if (props.type === 'tags') {
    currentContext.tagNameList = [props.title];
  } else if (props.type === 'filter') {
    const searchParams =  props.searchParams;
    if (searchParams.taskGroupIdList && searchParams.taskGroupIdList.length > 0) {
      currentContext.groupId = searchParams.taskGroupIdList[0];
    }
    if (searchParams.tagNameList && searchParams.tagNameList.length > 0) {
      currentContext.tagNameList = searchParams.tagNameList[0];
    }
    if (searchParams.taskLevelList && searchParams.taskLevelList.length > 0) {
      currentContext.taskLevel = searchParams.taskLevelList[0];
    }
  } else if (props.id === 'collectBox') {
    currentContext.groupId = props.id;
  }
  context.value = currentContext

  console.log('loadContext', context.value);
}
const saveTask = useSaveTaskItem();
const taskItemDrag = useTaskItemDrag(data);
provide<ITaskViewProvide>(taskViewProvideCode, {
  context,
  refreshData,
  getScene() {
    return props.scene;
  },
  ...taskItemDrag,
  saveTask,
});
async function handleCleanAllDeleteTask() {
  Modal.confirm({
    title: '二次提醒',
    content: '确认要清理这些任务吗, 清理后将无法还原的',
    cancelText: '放弃',
    okText: '清理',
    okButtonProps: {
      status: 'danger',
    },
    async onOk() {
      setTimeout(() => {
        const mp3 = new Audio(new URL('@/assets/soundEffect/delete-all.mp3', import.meta.url).href);
        mp3.loop = false;
        mp3.volume = 0.1;
        mp3.play().finally(() => {
          mp3.remove();
        });
      })
      await ExtensionManager.getTaskInstance().cleanAllDeleteTask();
      refreshData().then(() => {});
    },
  });
}

function handleOpenAi() {
  const aiTaskDrawer = useAiTaskDrawer();
  aiTaskDrawer.show({
    ...props.searchParams,
  });
}

const finishData = ref<TaskListItem[]>([]);
function handleHideFinish() {
  viewConfig.value.showFinish = false;
  finishData.value.length = 0;
}

async function handleShowFinish() {
  viewConfig.value.showFinish = true;
  viewConfigChange()
  ExtensionManager.getTaskInstance().listTask({
    ...props.searchParams,
    searchType: 'finish',
  }).then(res => {
    finishData.value = res.list;
  })
}
</script>

<template>
  <a-page-header :title="title" @back="handleBack" :show-back="showBack" :subtitle="subtitle">
    <template #title>
      <div class="u-fx u-gap5 u-fac">
        <div>
          <slot name="header-title-prefix" />
        </div>
        <div>
          {{ title }}
        </div>
      </div>
    </template>
    <template #extra>
      <!-- 标题右边菜单 -->
      <div v-if="scene === 'default'" class="u-fx u-fac">
        <!--  AI 总结  -->
        <a-tooltip content="AI 总结"
                   mini>
          <a-button id="ai-summary" size="mini"
                    @click="handleOpenAi">
            <template #icon>
              <iconpark-icon name="ai" size="16" style="color: #9686F8;"></iconpark-icon>
            </template>
          </a-button>
        </a-tooltip>
        <TaskSortDropdown
          v-model:group-type="viewConfig.groupType"
          v-model:sort-type="viewConfig.sortType"
        />
        <a-dropdown class="min-dropdown-select" position="tr">
          <a-button size="small" class="view-more-button">
            <template #icon>
              <iconpark-icon size="14" name="more"></iconpark-icon>
            </template>
          </a-button>
          <template #content>
            <div class="min-dropdown-select-options view-more">
              <div>
                <div class="u-tips" style="padding-bottom: 4px">视图</div>
                <a-radio-group v-model:model-value="viewMode"
                               size="mini"
                               type="button">
                  <a-tooltip mini>
                    <template #content>看板模式</template>
                    <a-radio value="grid">
                      <iconpark-icon
                        name="column"
                        size="14"
                        style="padding-top: 4px"
                      ></iconpark-icon>
                    </a-radio>
                  </a-tooltip>
                  <a-tooltip mini>
                    <template #content>列表模式</template>
                    <a-radio value="list">
                      <iconpark-icon
                        name="mindmap-list"
                        size="14"
                        style="padding-top: 4px"
                      ></iconpark-icon>
                    </a-radio>
                  </a-tooltip>
                  <!--                  <a-tooltip mini>
                                      <template #content>列表模式</template>
                                      <a-radio value="list">
                                        <iconpark-icon
                                          name="mindmap-list"
                                          size="14"
                                          style="padding-top: 4px"
                                        ></iconpark-icon>
                                      </a-radio>
                                    </a-tooltip>-->
                </a-radio-group>
                <div class="u-tips" style="margin-top: 6px;">
                  <a-doption v-if="viewConfig.showFinish"
                             @click="handleHideFinish">
                    <div class="u-fx u-fac u-gap5">
                      <iconpark-icon name="preview-close"></iconpark-icon>
                      <div>隐藏已完成</div>
                    </div>
                  </a-doption>
                  <a-doption>
                    <div v-if="!viewConfig.showFinish"
                         class="u-fx u-fac u-gap5"
                         @click="handleShowFinish">
                      <iconpark-icon name="eyes"></iconpark-icon>
                      <div>显示已完成</div>
                    </div>
                  </a-doption>
                </div>
              </div>
            </div>
          </template>
        </a-dropdown>
      </div>
      <div v-if="scene === 'delete'">
        <a-button
          id="clean-all-delete-task"
          type="text"
          status="danger"
          size="mini"
          shape="round"
          @click="handleCleanAllDeleteTask"
          :disabled="!data.length"
        >
          清空已删除
        </a-button>
      </div>
    </template>
    <div class="task-detail">
      <TaskViewLayer
        :viewMode="viewMode"
        :viewConfig="viewConfig"
        :scene="scene"
        :data="data"
        :finishData="finishData" />
    </div>
  </a-page-header>
</template>

<style lang="less" scoped>
.arco-page-header {
  box-sizing: border-box;
  padding: 0;
  :deep(.arco-page-header-wrapper) {
    padding: 0;
    margin-bottom: 6px;
  }
  :deep(.arco-page-header-content) {
    box-sizing: border-box;
    padding: 0;
  }
}

:deep(.arco-btn-secondary, .arco-btn-secondary[type='button'], .arco-btn-secondary[type='submit']) {
  background-color: transparent;
}
:deep(.arco-btn-group .arco-btn-secondary:not(:last-child)) {
  border-right: none;
}
:deep(.arco-dropdown) {
  border: none !important;
}
</style>
