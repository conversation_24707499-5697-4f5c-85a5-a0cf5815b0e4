/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AButton: typeof import('@arco-design/web-vue')['Button']
    AButtonGroup: typeof import('@arco-design/web-vue')['ButtonGroup']
    ACheckbox: typeof import('@arco-design/web-vue')['Checkbox']
    ACollapse: typeof import('@arco-design/web-vue')['Collapse']
    ACollapseItem: typeof import('@arco-design/web-vue')['CollapseItem']
    ADatePicker: typeof import('@arco-design/web-vue')['DatePicker']
    ADivider: typeof import('@arco-design/web-vue')['Divider']
    ADoption: typeof import('@arco-design/web-vue')['Doption']
    ADrawer: typeof import('@arco-design/web-vue')['Drawer']
    ADropdown: typeof import('@arco-design/web-vue')['Dropdown']
    AEmpty: typeof import('@arco-design/web-vue')['Empty']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AiTaskDrawer: typeof import('./src/components/ai/AiTaskDrawer/AiTaskDrawer.vue')['default']
    ALink: typeof import('@arco-design/web-vue')['Link']
    AMenu: typeof import('@arco-design/web-vue')['Menu']
    AMenuItem: typeof import('@arco-design/web-vue')['MenuItem']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    AOptgroup: typeof import('@arco-design/web-vue')['Optgroup']
    AOption: typeof import('@arco-design/web-vue')['Option']
    APageHeader: typeof import('@arco-design/web-vue')['PageHeader']
    APopconfirm: typeof import('@arco-design/web-vue')['Popconfirm']
    ARadio: typeof import('@arco-design/web-vue')['Radio']
    ARadioGroup: typeof import('@arco-design/web-vue')['RadioGroup']
    ARangePicker: typeof import('@arco-design/web-vue')['RangePicker']
    AScrollbar: typeof import('@arco-design/web-vue')['Scrollbar']
    ASelect: typeof import('@arco-design/web-vue')['Select']
    ASplit: typeof import('@arco-design/web-vue')['Split']
    ASwitch: typeof import('@arco-design/web-vue')['Switch']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    ATextarea: typeof import('@arco-design/web-vue')['Textarea']
    ATimePicker: typeof import('@arco-design/web-vue')['TimePicker']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    ATrigger: typeof import('@arco-design/web-vue')['Trigger']
    ATypographyParagraph: typeof import('@arco-design/web-vue')['TypographyParagraph']
    DropIndicator: typeof import('./src/components/Tasks/TaskItem/DropIndicator.vue')['default']
    EditorTiptap: typeof import('./src/components/editor/EditorTiptap.vue')['default']
    EventItem: typeof import('./src/components/calendar/EventItem/EventItem.vue')['default']
    GroupBoxCard: typeof import('./src/components/card/GroupBoxCard/GroupBoxCard.vue')['default']
    GroupButtonSelect: typeof import('./src/components/group/groupSelect/GroupButtonSelect.vue')['default']
    GroupSelectDropdown: typeof import('./src/components/group/groupSelect/GroupSelectDropdown.vue')['default']
    HeadlineSelect: typeof import('./src/components/editor/extensions/HeadlineSelect.vue')['default']
    ImageCropper: typeof import('./src/components/image/ImageCropper/ImageCropper.vue')['default']
    LazyLoader: typeof import('./src/components/common/container/LazyLoader/LazyLoader.vue')['default']
    LeftMenu: typeof import('./src/components/common/LeftMenu/LeftMenu.vue')['default']
    LeftMenuSplitPanel: typeof import('./src/components/common/container/LeftMenuSplitPanel/LeftMenuSplitPanel.vue')['default']
    LinkEditBubble: typeof import('./src/components/editor/extensions/LinkTool/LinkEditBubble.vue')['default']
    LinkTool: typeof import('./src/components/editor/extensions/LinkTool/LinkTool.vue')['default']
    PromptManager: typeof import('./src/components/ai/AiTaskDrawer/PromptManager.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SlashCommandsList: typeof import('./src/components/editor/extensions/SlashCommandsList.vue')['default']
    SvgIcon: typeof import('./src/components/common/SvgIcon/SvgIcon.vue')['default']
    TableToolbar: typeof import('./src/components/editor/extensions/TableToolbar.vue')['default']
    TableToolButton: typeof import('./src/components/editor/extensions/TableToolButton.vue')['default']
    TaskCheck: typeof import('./src/components/check/TaskCheck/TaskCheck.vue')['default']
    TaskContextMenu: typeof import('./src/components/Tasks/TaskItem/TaskContextMenu.vue')['default']
    TaskCreateInput: typeof import('./src/components/Tasks/TaskCreateInput/TaskCreateInput.vue')['default']
    TaskCreateInputTips: typeof import('./src/components/Tasks/TaskCreateInput/TaskCreateInputTips.vue')['default']
    TaskDateTimeTrigger: typeof import('./src/components/Tasks/TaskCreateInput/TaskDateTimeTrigger.vue')['default']
    TaskDetailDrawer: typeof import('./src/components/Tasks/TaskDetailDrawer/TaskDetailDrawer.vue')['default']
    TaskDetailTrigger: typeof import('./src/components/Tasks/TaskDetailTrigger/TaskDetailTrigger.vue')['default']
    TaskDragPreview: typeof import('./src/components/Tasks/TaskItem/TaskDragPreview.vue')['default']
    TaskDropShadow: typeof import('./src/components/Tasks/TaskItem/TaskDropShadow.vue')['default']
    TaskItem: typeof import('./src/components/Tasks/TaskItem/TaskItem.vue')['default']
    TaskLevelDropdown: typeof import('./src/components/Tasks/TaskLevelDropdown/TaskLevelDropdown.vue')['default']
    TaskSortDropdown: typeof import('./src/components/Tasks/TaskSortDropdown/TaskSortDropdown.vue')['default']
    TaskStatusSelect: typeof import('./src/components/Tasks/TaskStatusSelect/TaskStatusSelect.vue')['default']
    TaskTagDropdown: typeof import('./src/components/Tasks/TaskCreateInput/TaskTagDropdown.vue')['default']
    TaskView: typeof import('./src/components/Tasks/TaskView/TaskView.vue')['default']
    TextBubbleMenu: typeof import('./src/components/editor/extensions/TextBubbleMenu.vue')['default']
    UContextMenu: typeof import('./src/components/common/contextMenu/UContextMenu.vue')['default']
    UtoolsSearchView: typeof import('./src/components/Tasks/SearchTaskView/UtoolsSearchView.vue')['default']
    WheelWaveContainer: typeof import('./src/components/common/container/WheelWaveContainer/WheelWaveContainer.vue')['default']
  }
}
