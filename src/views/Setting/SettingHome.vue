<script setup lang="ts">
import { useTaskSetting } from './TaskSetting.ts';

const { taskFinishSoundEffect, pluginHeight, guideDisable } = useTaskSetting();

function handleUtoolsHeight() {
  utools.setExpendHeight(pluginHeight.value);
}
</script>

<template>
  <div>
    <a-page-header title="设置中心" :show-back="false"> </a-page-header>
    <div class="setting">
      <div class="u-fx u-fac u-f-between setting-item u-pointer"
           @click="taskFinishSoundEffect = !taskFinishSoundEffect">
        <div class="u-fx u-fac u-gap5">
          <iconpark-icon
            :name="taskFinishSoundEffect ? 'volume-notice' : 'volume-mute'"
            :style="{color: taskFinishSoundEffect ? 'rgb(var(--green-5))' : ''}"
          />
          <div>任务完成音效</div>
        </div>
        <div>
          <a-switch style="width: 48px;"
                    v-model:model-value="taskFinishSoundEffect"
                    checked-color="rgb(var(--green-5))"
                    @click.stop />
        </div>
      </div>
      <div class="u-fx u-fac u-f-between setting-item u-pointer"
           @click="guideDisable = !guideDisable">
        <div class="u-fx u-fac u-gap5">
          <iconpark-icon
            name="info"
            :style="{color: !guideDisable ? 'rgb(var(--green-5))' : ''}"
          />
          <div>引导显示</div>
        </div>
        <div>
          <a-switch style="width: 48px;"
                    :model-value="!guideDisable"
                    checked-color="rgb(var(--green-5))"
                    @change="(value: any) => (guideDisable = !value)"
                    @click.stop />
        </div>
      </div>
      <div class="setting-item vertical">
       <div class="u-fx u-fac u-f-between">
         <div class="u-fx u-fac u-gap5" style="height: 46px;">
           <iconpark-icon name="height"></iconpark-icon>
           <div>插件高度</div>
         </div>
         <div style="height: 24px;">
           <a-input-number
             class="u-transparent"
             v-model:model-value="pluginHeight"
             style="width: 60px"
             :min="500"
             :max="1200"
             @change="handleUtoolsHeight()"
             hide-button
           />
         </div>
       </div>
        <div style="padding: 10px">
          <a-slider  v-model:model-value="pluginHeight"
                     :min="500"
                     :max="1200"
                     :show-tooltip="false"
                     @change="handleUtoolsHeight()"></a-slider>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.setting {
  padding: 10px 32px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.setting-item {
  padding: 0 12px;
  min-height: 56px;
  border-radius: 10px;
  background-color: var(--color-bg-3);
  transition: all 380ms linear;
  &.vertical {
    display: flex;
    flex-direction: column;
  }
  &:hover {
    background-color: rgba(var(--gray-3), 0.65);
    box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
  }
  iconpark-icon {
    font-size: 18px;
    transition: all 300ms ease;
  }

  :deep(.arco-slider-track::before){
    height: 8px;
    border-radius: 4px;
    background: var(--color-neutral-4);
  }

  :deep(.arco-slider-btn) {
    width: 16px;
    height: 16px;
    transform: translate(-50%, -14%);
  }
  :deep(.arco-slider-btn::after) {
    width: 16px;
    height: 16px;
    background: #12B981;
    border: none;
  }
  :deep(.arco-slider-bar) {
    height: 8px;
    border-radius: 4px;
    background: var(--color-neutral-4);
  }
}
</style>
