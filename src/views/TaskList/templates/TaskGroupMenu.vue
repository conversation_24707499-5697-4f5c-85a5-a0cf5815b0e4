<script setup lang="ts">
import type { IMenuItem } from './index.ts';
import { h, nextTick, onMounted, ref, toRefs, useTemplateRef } from 'vue';
import { ExtensionManager } from '@/extension';
import type { IGroupListResult } from '@xiaou66/todo-plugin';
import { useEventListener } from '@vueuse/core';
import GroupBoxSaveDrawer from "@/views/GroupBox/templates/GroupBoxSaveDrawer.vue";
import type {GroupBoxSaveDrawerInstance} from "@/views/GroupBox/templates/GroupBoxSaveDrawerInstance.ts";
import type { IGroupInfo } from '@xiaou66/todo-plugin';
import { Modal } from '@arco-design/web-vue';
import { useCollapse } from '@/hooks/useCollapse.ts';
import { FlowExecutor } from '@/views/CollectScene/flow';

const props = defineProps<{
  setCategoryList: (menuItems: IMenuItem[]) => void
}>();

const groupData = ref<IGroupListResult>({
  total: 0,
  list: []
});

async function request() {
  const groupInstance = ExtensionManager.getGroupInstance();
  groupData.value = await groupInstance.listGroup({});
  props.setCategoryList(groupData.value.list.map(item => {
    return {
      id: item.groupId,
      name: item.groupName,
      desc: '',
      menuType: 'group',
      searchParams: {
        groupId: item.groupId
      }
    } as IMenuItem
  }));
  nextTick(() => {
    calcCollapseHeight();
  }).then(() => {});
  handleStatisticsCategoriesCount();
}
onMounted(() => {
  request();
});
function handleGroupSaveAfter(groupId: string) {
  collapsed.value = false;
  console.log(groupId);
  request().then(() => {
    active.value = groupId;
  });
}
const active = defineModel<string>('active');


const groupStatistics = ref<{
  [key: string]: number;
}>({});


function handleStatisticsCategoriesCount() {
  groupData.value.list.map(async (item) => {
    await ExtensionManager.getTaskInstance().listTask({
      groupId: item.groupId
    }).then(res => {
      groupStatistics.value[item.groupId] = res.total;
      // item.data = res.list;
    });
  });
}

function handleDeleteGroup(group: IGroupInfo) {
  Modal.confirm({
    title: '删除分组提醒',
    content: () => h('div', {}, [
      h('div', {}, `确定要删除「${group.groupName}」分组吗？`),
      h('div', {  }, [
        h('span', { style: 'color: #f5222d' }, `分组内的任务彻底删除, `),
        h('span', {style: 'font-weight: bold; color: #f5222d'}, '无法还原')
      ])
    ]),
    cancelText: '放弃',
    okText: '确认删除',
    okButtonProps: {
      status: 'danger'
    },
    onOk: async () => {
      await ExtensionManager.getGroupInstance().deleteGroup(group.groupId);
      await FlowExecutor.adjustFlowNodeData();
      await request();
      if (active.value === group.groupId) {
        // 当前是删除的分组
        active.value = 'toDay';
      }
    }
  });
}
const groupBoxSaveDrawerRef = useTemplateRef<GroupBoxSaveDrawerInstance>('groupBoxSaveDrawerRef');
const [collapsed, handleCollapse, calcCollapseHeight] = useCollapse();
useEventListener(window, 'taskList::refreshAll', () => {
  if (!collapsed.value) {
    request();
  }
});
</script>

<template>
  <GroupBoxSaveDrawer ref="groupBoxSaveDrawerRef"
                      @saveAfter="handleGroupSaveAfter" />
  <!--  分组  -->
  <div class="sidebar-group">
    <div class="sidebar-section">
      <div class="section-title"
           @click="handleCollapse">
        <div class="u-fx u-fac u-pos-rel" >
          <iconpark-icon :name="collapsed ? 'right' : 'down'"
                         :size="15" style="position:relative; left: -2px;"></iconpark-icon>
          <div>分组</div>
        </div>
        <div class="u-fx u-fac">
          <iconpark-icon name="plus"
                         @click.stop="groupBoxSaveDrawerRef?.show()"></iconpark-icon>
        </div>
      </div>
    </div>
    <div class="sidebar-section item"
         :class="{ collapsed }">
      <div  ref="collapseRef">
        <a-dropdown v-for="group in groupData.list"
                    :key="group.groupId"
                    class="u-transparent min-dropdown-select"
                    trigger="contextMenu"
                    alignPoint>
          <div class="sidebar-item"
               :class="{ active: active === group.groupId }"
               @click="active = group.groupId">
            <!--              <div class="icon">🐝</div>-->
            <div class="name">{{ group.groupName }}</div>
            <div class="count">{{ groupStatistics[group.groupId] || 0 }}</div>
          </div>
          <template #content>
            <div class="min-dropdown-select-options">
              <a-doption @click="groupBoxSaveDrawerRef?.show(group)">
                <template #icon>
                  <iconpark-icon name="write"
                                 style="color: var(--color-neutral-8)" />
                </template>
                编辑
              </a-doption>
              <a-divider style="margin: 4px 0; padding: 0"></a-divider>
              <a-doption @click="handleDeleteGroup(group)">
                <template #icon>
                  <iconpark-icon name="delete"
                                 style="color: var(--color-neutral-8)" />
                </template>
                删除
              </a-doption>
            </div>
          </template>
        </a-dropdown>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
